[{"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\layout.tsx": "1", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\page.tsx": "2", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Footer.tsx": "3", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Navigation.tsx": "4", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\AboutSection.tsx": "5", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ContactSection.tsx": "6", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ExperienceSection.tsx": "7", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\HeroSection.tsx": "8", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ProjectsSection.tsx": "9", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ServicesSection.tsx": "10", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\SkillsSection.tsx": "11", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\StatsSection.tsx": "12"}, {"size": 1014, "mtime": 1749524647817, "results": "13", "hashOfConfig": "14"}, {"size": 1012, "mtime": 1749524977589, "results": "15", "hashOfConfig": "14"}, {"size": 7714, "mtime": 1749524928530, "results": "16", "hashOfConfig": "14"}, {"size": 3947, "mtime": 1749524711766, "results": "17", "hashOfConfig": "14"}, {"size": 5605, "mtime": 1749524760823, "results": "18", "hashOfConfig": "14"}, {"size": 5974, "mtime": 1749524894905, "results": "19", "hashOfConfig": "14"}, {"size": 5951, "mtime": 1749524791922, "results": "20", "hashOfConfig": "14"}, {"size": 5789, "mtime": 1749524736058, "results": "21", "hashOfConfig": "14"}, {"size": 6723, "mtime": 1749524867158, "results": "22", "hashOfConfig": "14"}, {"size": 5117, "mtime": 1749524838914, "results": "23", "hashOfConfig": "14"}, {"size": 4827, "mtime": 1749524814000, "results": "24", "hashOfConfig": "14"}, {"size": 4913, "mtime": 1749524950853, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ecp3ba", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\layout.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\page.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Footer.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\AboutSection.tsx", ["62"], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ContactSection.tsx", ["63", "64"], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ExperienceSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\HeroSection.tsx", ["65"], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ProjectsSection.tsx", ["66", "67", "68"], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ServicesSection.tsx", ["69"], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\SkillsSection.tsx", ["70"], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\StatsSection.tsx", ["71"], [], {"ruleId": "72", "severity": 2, "message": "73", "line": 116, "column": 72, "nodeType": "74", "messageId": "75", "suggestions": "76"}, {"ruleId": "77", "severity": 2, "message": "78", "line": 75, "column": 35, "nodeType": null, "messageId": "79", "endLine": 75, "endColumn": 40}, {"ruleId": "72", "severity": 2, "message": "73", "line": 140, "column": 16, "nodeType": "74", "messageId": "75", "suggestions": "80"}, {"ruleId": "72", "severity": 2, "message": "73", "line": 46, "column": 16, "nodeType": "74", "messageId": "75", "suggestions": "81"}, {"ruleId": "72", "severity": 2, "message": "73", "line": 90, "column": 57, "nodeType": "74", "messageId": "75", "suggestions": "82"}, {"ruleId": "77", "severity": 2, "message": "78", "line": 100, "column": 35, "nodeType": null, "messageId": "79", "endLine": 100, "endColumn": 40}, {"ruleId": "72", "severity": 2, "message": "73", "line": 185, "column": 16, "nodeType": "74", "messageId": "75", "suggestions": "83"}, {"ruleId": "77", "severity": 2, "message": "78", "line": 96, "column": 35, "nodeType": null, "messageId": "79", "endLine": 96, "endColumn": 40}, {"ruleId": "72", "severity": 2, "message": "73", "line": 52, "column": 57, "nodeType": "74", "messageId": "75", "suggestions": "84"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 62, "column": 6, "nodeType": "87", "endLine": 62, "endColumn": 14, "suggestions": "88"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["89", "90", "91", "92"], "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar", ["93", "94", "95", "96"], ["97", "98", "99", "100"], ["101", "102", "103", "104"], ["105", "106", "107", "108"], ["109", "110", "111", "112"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'stats'. Either include it or remove the dependency array.", "ArrayExpression", ["113"], {"messageId": "114", "data": "115", "fix": "116", "desc": "117"}, {"messageId": "114", "data": "118", "fix": "119", "desc": "120"}, {"messageId": "114", "data": "121", "fix": "122", "desc": "123"}, {"messageId": "114", "data": "124", "fix": "125", "desc": "126"}, {"messageId": "114", "data": "127", "fix": "128", "desc": "117"}, {"messageId": "114", "data": "129", "fix": "130", "desc": "120"}, {"messageId": "114", "data": "131", "fix": "132", "desc": "123"}, {"messageId": "114", "data": "133", "fix": "134", "desc": "126"}, {"messageId": "114", "data": "135", "fix": "136", "desc": "117"}, {"messageId": "114", "data": "137", "fix": "138", "desc": "120"}, {"messageId": "114", "data": "139", "fix": "140", "desc": "123"}, {"messageId": "114", "data": "141", "fix": "142", "desc": "126"}, {"messageId": "114", "data": "143", "fix": "144", "desc": "117"}, {"messageId": "114", "data": "145", "fix": "146", "desc": "120"}, {"messageId": "114", "data": "147", "fix": "148", "desc": "123"}, {"messageId": "114", "data": "149", "fix": "150", "desc": "126"}, {"messageId": "114", "data": "151", "fix": "152", "desc": "117"}, {"messageId": "114", "data": "153", "fix": "154", "desc": "120"}, {"messageId": "114", "data": "155", "fix": "156", "desc": "123"}, {"messageId": "114", "data": "157", "fix": "158", "desc": "126"}, {"messageId": "114", "data": "159", "fix": "160", "desc": "117"}, {"messageId": "114", "data": "161", "fix": "162", "desc": "120"}, {"messageId": "114", "data": "163", "fix": "164", "desc": "123"}, {"messageId": "114", "data": "165", "fix": "166", "desc": "126"}, {"desc": "167", "fix": "168"}, "replaceWithAlt", {"alt": "169"}, {"range": "170", "text": "171"}, "Replace with `&apos;`.", {"alt": "172"}, {"range": "173", "text": "174"}, "Replace with `&lsquo;`.", {"alt": "175"}, {"range": "176", "text": "177"}, "Replace with `&#39;`.", {"alt": "178"}, {"range": "179", "text": "180"}, "Replace with `&rsquo;`.", {"alt": "169"}, {"range": "181", "text": "182"}, {"alt": "172"}, {"range": "183", "text": "184"}, {"alt": "175"}, {"range": "185", "text": "186"}, {"alt": "178"}, {"range": "187", "text": "188"}, {"alt": "169"}, {"range": "189", "text": "182"}, {"alt": "172"}, {"range": "190", "text": "184"}, {"alt": "175"}, {"range": "191", "text": "186"}, {"alt": "178"}, {"range": "192", "text": "188"}, {"alt": "169"}, {"range": "193", "text": "194"}, {"alt": "172"}, {"range": "195", "text": "196"}, {"alt": "175"}, {"range": "197", "text": "198"}, {"alt": "178"}, {"range": "199", "text": "200"}, {"alt": "169"}, {"range": "201", "text": "202"}, {"alt": "172"}, {"range": "203", "text": "204"}, {"alt": "175"}, {"range": "205", "text": "206"}, {"alt": "178"}, {"range": "207", "text": "208"}, {"alt": "169"}, {"range": "209", "text": "210"}, {"alt": "172"}, {"range": "211", "text": "212"}, {"alt": "175"}, {"range": "213", "text": "214"}, {"alt": "178"}, {"range": "215", "text": "216"}, "Update the dependencies array to be: [inView, stats]", {"range": "217", "text": "218"}, "&apos;", [3086, 3495], "\n              My journey into web development started with a fascination for how the web works, \n              leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as \n              React and Angular on the front end. On the server side, I&apos;m skilled in PHP, Node.js, \n              and MySQL, with a particular focus on building scalable and secure web applications.\n            ", "&lsquo;", [3086, 3495], "\n              My journey into web development started with a fascination for how the web works, \n              leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as \n              React and Angular on the front end. On the server side, I&lsquo;m skilled in PHP, Node.js, \n              and MySQL, with a particular focus on building scalable and secure web applications.\n            ", "&#39;", [3086, 3495], "\n              My journey into web development started with a fascination for how the web works, \n              leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as \n              React and Angular on the front end. On the server side, I&#39;m skilled in PHP, Node.js, \n              and MySQL, with a particular focus on building scalable and secure web applications.\n            ", "&rsquo;", [3086, 3495], "\n              My journey into web development started with a fascination for how the web works, \n              leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as \n              React and Angular on the front end. On the server side, I&rsquo;m skilled in PHP, Node.js, \n              and MySQL, with a particular focus on building scalable and secure web applications.\n            ", [4730, 4749], "\n              I&apos;m ", [4730, 4749], "\n              I&lsquo;m ", [4730, 4749], "\n              I&#39;m ", [4730, 4749], "\n              I&rsquo;m ", [1544, 1563], [1544, 1563], [1544, 1563], [1544, 1563], [2710, 2753], "Here&apos;s Some of My Project that already done", [2710, 2753], "Here&lsquo;s Some of My Project that already done", [2710, 2753], "Here&#39;s Some of My Project that already done", [2710, 2753], "Here&rsquo;s Some of My Project that already done", [6577, 6620], "\n            Let&apos;s Work Together\n          ", [6577, 6620], "\n            Let&lsquo;s Work Together\n          ", [6577, 6620], "\n            Let&#39;s Work Together\n          ", [6577, 6620], "\n            Let&rsquo;s Work Together\n          ", [1550, 1576], "here&apos;s some my major skill", [1550, 1576], "here&lsquo;s some my major skill", [1550, 1576], "here&#39;s some my major skill", [1550, 1576], "here&rsquo;s some my major skill", [1365, 1373], "[inView, stats]"]