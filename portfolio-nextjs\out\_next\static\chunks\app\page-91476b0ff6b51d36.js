(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{180:(e,t,i)=>{"use strict";i.d(t,{default:()=>h});var a=i(5155),s=i(1934),n=i(3096),r=i(9621),l=i(4355),o=i(463),c=i(2713),d=i(3127),m=i(6785);let h=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),i=[{icon:r.A,title:"Web Design",description:"Creating beautiful and functional web designs that engage users and drive results."},{icon:l.A,title:"Photography",description:"Professional photography services for events, portraits, and commercial projects."},{icon:o.A,title:"Web Developer",description:"Full-stack web development using modern technologies and best practices."},{icon:c.A,title:"App Developing",description:"Mobile and web application development with focus on user experience."},{icon:d.A,title:"Branding",description:"Complete branding solutions including logo design and brand identity."},{icon:m.A,title:"Product Strategy",description:"Strategic planning and consultation for digital product development."}],h={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,a.jsx)("section",{id:"services-section",className:"py-20 bg-white",ref:e,children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,a.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Services"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"Services"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 text-lg max-w-2xl mx-auto",children:"Far far away, behind the word mountains, far from the countries Vokalia and Consonantia"})]}),(0,a.jsx)(s.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:i.map(e=>{let t=e.icon;return(0,a.jsx)(s.P.div,{className:"group",variants:h,children:(0,a.jsxs)(s.P.div,{className:"bg-gray-50 hover:bg-blue-50 p-8 rounded-lg text-center transition-all duration-300 cursor-pointer h-full",whileHover:{y:-10,boxShadow:"0 20px 40px rgba(0,0,0,0.1)"},transition:{duration:.3},children:[(0,a.jsx)(s.P.div,{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 group-hover:bg-blue-200 rounded-full mb-6 transition-colors duration-300",whileHover:{scale:1.1,rotate:5},transition:{duration:.3},children:(0,a.jsx)(t,{size:32,className:"text-blue-600 group-hover:text-blue-700 transition-colors duration-300"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4 group-hover:text-blue-700 transition-colors duration-300",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})},e.title)})}),(0,a.jsx)(s.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:1},children:(0,a.jsx)(s.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Get Started"})})]})})}},1911:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var a=i(5155),s=i(1934),n=i(3096),r=i(6766),l=i(2115);let o=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),[i,o]=(0,l.useState)(0);(0,l.useEffect)(()=>{if(t){let e=0,t=setInterval(()=>{(e+=5.84)>=730?(o(730),clearInterval(t)):o(Math.floor(e))},16);return()=>clearInterval(t)}},[t]);let c={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},d={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,a.jsx)("section",{id:"about-section",className:"py-20 bg-gray-50",ref:e,children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)(s.P.div,{className:"grid md:grid-cols-2 gap-12 items-center",variants:c,initial:"hidden",animate:t?"visible":"hidden",children:[(0,a.jsx)(s.P.div,{className:"relative",variants:d,children:(0,a.jsxs)("div",{className:"relative w-full max-w-md mx-auto",children:[(0,a.jsx)(s.P.div,{whileHover:{scale:1.02},transition:{duration:.3},children:(0,a.jsx)(r.default,{src:"/images/aboutme.png",alt:"About Rizqi Rahmansyah",width:400,height:500,className:"w-full h-auto rounded-lg shadow-xl"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-blue-600/20 to-transparent rounded-lg"})]})}),(0,a.jsxs)(s.P.div,{className:"space-y-6",variants:d,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(s.P.h1,{className:"text-6xl font-bold text-gray-200 mb-2",variants:d,children:"About"}),(0,a.jsx)(s.P.h2,{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",variants:d,children:"About Me"})]}),(0,a.jsx)(s.P.p,{className:"text-gray-600 leading-relaxed text-lg",variants:d,children:"My journey into web development started with a fascination for how the web works, leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as React and Angular on the front end. On the server side, I'm skilled in PHP, Node.js, and MySQL, with a particular focus on building scalable and secure web applications."}),(0,a.jsx)(s.P.ul,{className:"space-y-3",variants:c,children:[{label:"Name:",value:"Rizqi Rahmansyah"},{label:"Date of birth:",value:"December 10, 1998"},{label:"Address:",value:"Bekasi"},{label:"Phone:",value:"+6281293062103"}].map((e,t)=>(0,a.jsxs)(s.P.li,{className:"flex text-gray-700",variants:d,children:[(0,a.jsx)("span",{className:"font-semibold w-32",children:e.label}),(0,a.jsx)("span",{children:e.value})]},t))}),(0,a.jsxs)(s.P.div,{className:"bg-white p-6 rounded-lg shadow-lg",variants:d,children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(s.P.div,{className:"text-4xl font-bold text-blue-600 mb-2",initial:{scale:0},animate:t?{scale:1}:{scale:0},transition:{duration:.5,delay:.5},children:i}),(0,a.jsx)("div",{className:"text-gray-600 font-medium",children:"Days Working Experience"})]}),(0,a.jsx)(s.P.div,{className:"mt-6",variants:d,children:(0,a.jsx)(s.P.a,{href:"#",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Download CV"})})]})]})]})})})}},2548:(e,t,i)=>{"use strict";i.d(t,{default:()=>h});var a=i(5155),s=i(1934),n=i(8175),r=i(488),l=i(5684),o=i(2138),c=i(1366),d=i(8883),m=i(1976);let h=()=>{let e=new Date().getFullYear(),t=[{icon:n.A,href:"https://twitter.com/rizqibennington",label:"Twitter"},{icon:r.A,href:"https://facebook.com/rizqirahmansyah10",label:"Facebook"},{icon:l.A,href:"https://instagram.com/rizqibennington",label:"Instagram"}],i=e=>{let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})};return(0,a.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"About"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6 leading-relaxed",children:"Want to connect with me? feel free to follow my social media"}),(0,a.jsx)("div",{className:"flex space-x-4",children:t.map((e,t)=>{let i=e.icon;return(0,a.jsx)(s.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-colors",whileHover:{scale:1.1,y:-2},whileTap:{scale:.95},initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},viewport:{once:!0},children:(0,a.jsx)(i,{size:18})},e.label)})})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Links"}),(0,a.jsx)("ul",{className:"space-y-3",children:[{href:"#home-section",label:"Home"},{href:"#about-section",label:"About"},{href:"#services-section",label:"Services"},{href:"#projects-section",label:"Projects"},{href:"#contact-section",label:"Contact"}].map((e,t)=>(0,a.jsx)(s.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.3,delay:.1*t},viewport:{once:!0},children:(0,a.jsxs)("a",{href:e.href,onClick:t=>{t.preventDefault(),i(e.href)},className:"text-gray-400 hover:text-white transition-colors flex items-center group",children:[(0,a.jsx)(o.A,{size:16,className:"mr-2 group-hover:translate-x-1 transition-transform"}),e.label]})},e.href))})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Services"}),(0,a.jsx)("ul",{className:"space-y-3",children:["Web Design","Web Development","Business Strategy","Data Analysis","Graphic Design"].map((e,t)=>(0,a.jsx)(s.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.3,delay:.1*t},viewport:{once:!0},children:(0,a.jsxs)("a",{href:"#services-section",onClick:e=>{e.preventDefault(),i("#services-section")},className:"text-gray-400 hover:text-white transition-colors flex items-center group",children:[(0,a.jsx)(o.A,{size:16,className:"mr-2 group-hover:translate-x-1 transition-transform"}),e]})},e))})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},viewport:{once:!0},children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Have a Questions?"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(s.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-gray-400 hover:text-white transition-colors group",whileHover:{x:5},children:[(0,a.jsx)(c.A,{size:20,className:"mr-3 text-green-500"}),(0,a.jsx)("span",{children:"+6281293062103"})]}),(0,a.jsxs)(s.P.a,{href:"mailto:<EMAIL>?subject=Need%20Help&body=Hi%20Rizqi,%20i%20need%20your%20help",className:"flex items-center text-gray-400 hover:text-white transition-colors group",whileHover:{x:5},children:[(0,a.jsx)(d.A,{size:20,className:"mr-3 text-blue-500"}),(0,a.jsx)("span",{children:"<EMAIL>"})]})]})]})]}),(0,a.jsx)(s.P.div,{className:"border-t border-gray-800 mt-12 pt-8 text-center",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.8},viewport:{once:!0},children:(0,a.jsxs)("p",{className:"text-gray-400",children:["Copyright \xa9 ",e," All rights reserved | This template is made with"," ",(0,a.jsx)(m.A,{size:16,className:"inline text-red-500 mx-1"})," by"," ",(0,a.jsx)("a",{href:"https://colorlib.com",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"Colorlib"})]})})]})})}},3286:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var a=i(5155),s=i(1934),n=i(3096),r=i(6766),l=i(3786);let o=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,a.jsx)("section",{id:"projects-section",className:"py-20 bg-gray-50",ref:e,children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,a.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Projects"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"My Projects"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"Here's Some of My Project that already done"})]}),(0,a.jsx)(s.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{title:"Katadata ESG Index",subtitle:"Katadata Green",image:"/images/katadata-esg-index.png",description:"Environmental, Social, and Governance index platform for sustainable business evaluation.",link:"#"},{title:"Sistem Informasi Komite Etik dan Hukum Dharmais",subtitle:"SIKETIK",image:"/images/siketik.png",description:"Ethics and Legal Committee Information System for hospital management.",link:"#"},{title:"Indeks Saham",subtitle:"Databoks Katadata",image:"/images/marketdata.png",description:"Stock market index tracking and analysis platform.",link:"#"},{title:"Analisis Sentimen Debat Pilpres 2024",subtitle:"Katadata Pemilu 2024",image:"/images/analisis-sentimen.png",description:"Sentiment analysis platform for 2024 presidential election debates.",link:"#"},{title:"Career Development Center BPSDMI",subtitle:"TopKarir x Kemenperin",image:"/images/bpsdmi.png",description:"Career development platform for industrial workforce.",link:"#"},{title:"Jabar Jawara Career Center",subtitle:"Disnakertrans Jabar X TopKarir",image:"/images/jabarjawara.png",description:"West Java career center platform for job seekers and employers.",link:"#"}].map(e=>(0,a.jsxs)(s.P.div,{className:"group relative overflow-hidden rounded-lg shadow-lg bg-white",variants:i,whileHover:{y:-5},transition:{duration:.3},children:[(0,a.jsxs)("div",{className:"relative h-64 overflow-hidden",children:[(0,a.jsx)(r.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-110"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsxs)("div",{className:"absolute bottom-4 left-4 right-4 text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-1 line-clamp-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-300 mb-3",children:e.subtitle}),(0,a.jsx)("p",{className:"text-sm text-gray-200 line-clamp-2 mb-3",children:e.description}),(0,a.jsxs)(s.P.a,{href:e.link,className:"inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:["View Project",(0,a.jsx)(l.A,{size:16})]})]})})]}),(0,a.jsxs)("div",{className:"p-6 md:hidden",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-blue-600 mb-2",children:e.subtitle}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,a.jsxs)("a",{href:e.link,className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-sm",children:["View Project",(0,a.jsx)(l.A,{size:16})]})]})]},e.title))}),(0,a.jsx)(s.P.div,{className:"text-center mt-12",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:1},children:(0,a.jsx)(s.P.a,{href:"#contact-section",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:e=>{var t;e.preventDefault(),null==(t=document.querySelector("#contact-section"))||t.scrollIntoView({behavior:"smooth",block:"start"})},children:"Let's Work Together"})})]})})}},5030:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var a=i(5155),s=i(1934),n=i(3096);let r=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,a.jsx)("section",{id:"skills-section",className:"py-20 bg-gray-50",ref:e,children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,a.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Skills"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"My Skills"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"here's some my major skill"})]}),(0,a.jsx)(s.P.div,{className:"grid md:grid-cols-2 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{name:"CSS",percentage:90,color:"bg-blue-500"},{name:"jQuery",percentage:85,color:"bg-green-500"},{name:"HTML5",percentage:95,color:"bg-orange-500"},{name:"PHP",percentage:90,color:"bg-purple-500"},{name:"Javascript",percentage:70,color:"bg-yellow-500"},{name:"Python",percentage:80,color:"bg-red-500"}].map((e,n)=>(0,a.jsxs)(s.P.div,{className:"bg-white p-6 rounded-lg shadow-lg",variants:i,whileHover:{y:-5},transition:{duration:.3},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:e.name}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-600",children:[e.percentage,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:(0,a.jsx)(s.P.div,{className:"h-full ".concat(e.color," rounded-full relative"),initial:{width:0},animate:t?{width:"".concat(e.percentage,"%")}:{width:0},transition:{duration:1.5,delay:.2*n,ease:"easeOut"},children:(0,a.jsx)(s.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent",initial:{x:"-100%"},animate:t?{x:"100%"}:{x:"-100%"},transition:{duration:1.5,delay:.2*n+.5,ease:"easeInOut"}})})})]},e.name))}),(0,a.jsxs)(s.P.div,{className:"mt-16 text-center",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:1},children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Other Technologies"}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-3",children:["React","Angular","Node.js","MySQL","PostgreSQL","Laravel","CodeIgniter","Git","Docker","AWS"].map((e,i)=>(0,a.jsx)(s.P.span,{className:"bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",initial:{opacity:0,scale:0},animate:t?{opacity:1,scale:1}:{opacity:0,scale:0},transition:{duration:.3,delay:1.2+.1*i,ease:"easeOut"},whileHover:{scale:1.1},children:e},e))})]})]})})}},6132:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var a=i(5155),s=i(1934),n=i(6766);let r=()=>{let e=e=>{let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})};return(0,a.jsxs)("section",{id:"home-section",className:"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/30"}),(0,a.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 items-center",children:[(0,a.jsxs)(s.P.div,{className:"text-white space-y-6",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,a.jsx)(s.P.span,{className:"text-lg text-blue-400 font-medium",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:"Hello!"}),(0,a.jsxs)(s.P.h1,{className:"text-4xl md:text-6xl font-bold leading-tight",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:["I'm ",(0,a.jsx)("span",{className:"text-blue-400",children:"Rizqi Rahmansyah"})]}),(0,a.jsx)(s.P.h2,{className:"text-2xl md:text-3xl text-gray-300 font-light",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},children:"A Web Developer Enthusiast"}),(0,a.jsxs)(s.P.div,{className:"flex flex-col sm:flex-row gap-4 pt-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.8},children:[(0,a.jsx)(s.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Hire me"}),(0,a.jsx)(s.P.button,{onClick:()=>e("#about-section"),className:"border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-medium transition-all inline-flex items-center justify-center",whileHover:{scale:1.05},whileTap:{scale:.95},children:"About Me"})]})]}),(0,a.jsx)(s.P.div,{className:"relative",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.3},children:(0,a.jsxs)("div",{className:"relative w-full max-w-md mx-auto",children:[(0,a.jsx)(s.P.div,{className:"relative z-10",whileHover:{scale:1.05},transition:{duration:.3},children:(0,a.jsx)(n.default,{src:"/images/me2.png",alt:"Rizqi Rahmansyah",width:400,height:500,className:"w-full h-auto rounded-lg shadow-2xl",priority:!0})}),(0,a.jsx)(s.P.div,{className:"absolute -top-4 -right-4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl",animate:{scale:[1,1.2,1],opacity:[.3,.6,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut"}}),(0,a.jsx)(s.P.div,{className:"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl",animate:{scale:[1.2,1,1.2],opacity:[.4,.2,.4]},transition:{duration:4,repeat:1/0,ease:"easeInOut"}})]})})]})}),(0,a.jsx)(s.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:1.2},children:(0,a.jsx)(s.P.div,{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,a.jsx)(s.P.div,{className:"w-1 h-3 bg-white rounded-full mt-2",animate:{opacity:[1,0,1]},transition:{duration:2,repeat:1/0}})})})]})}},7602:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var a=i(5155),s=i(1934),n=i(3096),r=i(4516),l=i(1366),o=i(8883);let c=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),i=[{icon:r.A,title:"Address",content:"Bekasi, West Java 17520",link:null},{icon:l.A,title:"Contact Number",content:"+6281293062103",link:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help"},{icon:o.A,title:"Email Address",content:"<EMAIL>",link:"mailto:<EMAIL>?subject=Need%20Help&body=Hi%20Rizqi,%20i%20need%20your%20help"}],c={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,a.jsx)("section",{id:"contact-section",className:"py-20 bg-white",ref:e,children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,a.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Contact"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"Contact Me"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"Need some help with your website? feel free to contact me! :)"})]}),(0,a.jsx)(s.P.div,{className:"grid md:grid-cols-3 gap-8 mb-16",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:i.map(e=>{let t=e.icon;return(0,a.jsx)(s.P.div,{className:"text-center",variants:c,children:(0,a.jsxs)(s.P.div,{className:"bg-gray-50 hover:bg-blue-50 p-8 rounded-lg transition-all duration-300",whileHover:{y:-5,boxShadow:"0 10px 30px rgba(0,0,0,0.1)"},transition:{duration:.3},children:[(0,a.jsx)(s.P.div,{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6",whileHover:{scale:1.1,rotate:5},transition:{duration:.3},children:(0,a.jsx)(t,{size:32,className:"text-blue-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:e.title}),e.link?(0,a.jsx)(s.P.a,{href:e.link,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-700 transition-colors",whileHover:{scale:1.05},children:e.content}):(0,a.jsx)("p",{className:"text-gray-600",children:e.content})]})},e.title)})}),(0,a.jsxs)(s.P.div,{className:"relative bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg overflow-hidden",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.8},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center opacity-20",style:{backgroundImage:"url(/images/bg_1.jpg)"}}),(0,a.jsxs)("div",{className:"relative z-10 text-center py-16 px-8",children:[(0,a.jsxs)(s.P.h2,{className:"text-3xl md:text-4xl font-bold text-white mb-4",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:1},children:["I'm ",(0,a.jsx)("span",{className:"text-yellow-300",children:"Available"})," for freelancing"]}),(0,a.jsx)(s.P.p,{className:"text-xl text-blue-100 mb-8",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:1.2},children:"Feel Free to contact me"}),(0,a.jsx)(s.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.6,delay:1.4},whileHover:{scale:1.05},whileTap:{scale:.95},children:"Contact Me Now"})]})]})]})})}},8261:(e,t,i)=>{"use strict";i.d(t,{default:()=>l});var a=i(5155),s=i(1934),n=i(3096),r=i(2115);let l=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),[i,l]=(0,r.useState)({certifications:0,experience:0,projects:0,industries:0}),o=[{key:"certifications",target:3,label:"Certification"},{key:"experience",target:2,label:"Years Working Experience"},{key:"projects",target:50,label:"Projects Completed"},{key:"industries",target:4,label:"Industry Fields"}];(0,r.useEffect)(()=>{t&&o.forEach(e=>{let t=0,i=e.target,a=i/125,s=setInterval(()=>{(t+=a)>=i?(l(t=>({...t,[e.key]:i})),clearInterval(s)):l(i=>({...i,[e.key]:Math.floor(t)}))},16)})},[t,o]);let c={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden",ref:e,children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,a.jsx)(s.P.div,{className:"grid grid-cols-2 md:grid-cols-4 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:o.map((e,n)=>(0,a.jsx)(s.P.div,{className:"text-center text-white",variants:c,children:(0,a.jsxs)(s.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300",whileHover:{scale:1.05,boxShadow:"0 10px 30px rgba(255,255,255,0.1)"},transition:{duration:.3},children:[(0,a.jsx)(s.P.div,{className:"text-4xl md:text-5xl font-bold mb-2",initial:{scale:0},animate:t?{scale:1}:{scale:0},transition:{duration:.5,delay:.2*n,type:"spring",stiffness:100},children:i[e.key]}),(0,a.jsx)(s.P.div,{className:"text-blue-100 font-medium text-sm md:text-base",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:.2*n+.3},children:e.label})]})},e.key))}),(0,a.jsx)(s.P.div,{className:"absolute top-10 left-10 w-20 h-20 bg-white/5 rounded-full blur-xl",animate:{scale:[1,1.2,1],opacity:[.3,.6,.3]},transition:{duration:4,repeat:1/0,ease:"easeInOut"}}),(0,a.jsx)(s.P.div,{className:"absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-xl",animate:{scale:[1.2,1,1.2],opacity:[.4,.2,.4]},transition:{duration:5,repeat:1/0,ease:"easeInOut"}})]})]})}},9112:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var a=i(5155),s=i(1934),n=i(3096);let r=()=>{let[e,t]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:.6}}};return(0,a.jsx)("section",{id:"resume-section",className:"py-20 bg-white",ref:e,children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,a.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Life Experience"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"Life Experience"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"Experience is the most valuable lesson from God"})]}),(0,a.jsx)(s.P.div,{className:"grid md:grid-cols-2 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{period:"2014 - 2017",title:"SMAN 2 Tambun Selatan",position:"Highschool of Science",description:"The first experience when I'm falling in love with IT Things."},{period:"2017 - 2021",title:"University of Singaperbangsa Karawang",position:"Bachelor's Degree of Computer Science",description:"My first step to begin my journey to be a developer. I started my coding journey here and graduated with a cum laude predicate with a GPA of 3.71."},{period:"January - June (2020)",title:"PT Telekomunikasi Indonesia (WITEL Karawang)",position:"Chatbot Developer",description:"My first working experience as an intern. Here, I developed a chatbot on Telegram to analyze, monitor, and count the jobs already done by technicians."},{period:"January 2022 - February 2023",title:"Topkarir Indonesia",position:"Fullstack Web Developer",description:"My first Working Experience in the professional field was in Topkarir, a startup company in Human Resource field, in this place my responsibility is Develop and Maintenance Website, Frontend, Backend, API and connect to third party API for Topkarir Website and Microsite there are Jaknaker.id, Jatimcerdas, jabarjawara, TopKarir, AUA Topkarir, CDC BPSDMI. Topkarir. often using MySQL and PostgreSQL ,PHP, Javascript, Jquery, CSS, HTML and GO. often using framework Codeigniter and Laravel"},{period:"March 2023 - March 2024",title:"Katadata Indonesia",position:"Web Developer",description:"Responsible for developing, maintaining, and testing the Katadata website, API, and client-side applications using frameworks like Laravel, CodeIgniter, and React JS, with programming languages such as PHP and JavaScript. Also involved in data scraping using Python to support the data engineering team. Katadata is a startup in the online media industry."},{period:"July 2024 - Now",title:"RS Nasional Kanker Dharmais",position:"Programmer",description:"Developed and maintained hospital management software, optimizing workflows and enhancing patient data management. Collaborated with medical staff to create custom applications for scheduling, electronic medical records (EMR), and reporting tools. Ensured data security and compliance with health regulations by implementing robust cybersecurity measures. Improved system performance and user experience through continuous code optimization and feature updates."}].map((e,n)=>(0,a.jsx)(s.P.div,{className:"relative",variants:i,children:(0,a.jsxs)(s.P.div,{className:"bg-gray-50 p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300",whileHover:{y:-5},transition:{duration:.3},children:[(0,a.jsx)(s.P.span,{className:"inline-block bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium mb-4",initial:{scale:0},animate:t?{scale:1}:{scale:0},transition:{duration:.3,delay:.1*n},children:e.period}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:e.title}),(0,a.jsx)("h4",{className:"text-blue-600 font-semibold mb-3",children:e.position}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})},n))}),(0,a.jsx)(s.P.div,{className:"text-center mt-12",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.8},children:(0,a.jsx)(s.P.a,{href:"#",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Download CV"})})]})})}},9600:(e,t,i)=>{Promise.resolve().then(i.bind(i,2548)),Promise.resolve().then(i.bind(i,9891)),Promise.resolve().then(i.bind(i,1911)),Promise.resolve().then(i.bind(i,7602)),Promise.resolve().then(i.bind(i,9112)),Promise.resolve().then(i.bind(i,6132)),Promise.resolve().then(i.bind(i,3286)),Promise.resolve().then(i.bind(i,180)),Promise.resolve().then(i.bind(i,5030)),Promise.resolve().then(i.bind(i,8261))},9891:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var a=i(5155),s=i(2115),n=i(1934),r=i(4416),l=i(4783);let o=()=>{let[e,t]=(0,s.useState)(!1),[i,o]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=()=>{o(window.scrollY>150)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let c=[{href:"#home-section",label:"Home"},{href:"#about-section",label:"About"},{href:"#resume-section",label:"Experience"},{href:"#services-section",label:"Services"},{href:"#skills-section",label:"Skills"},{href:"#projects-section",label:"Projects"},{href:"#contact-section",label:"Contact"}],d=e=>{let i=document.querySelector(e);i&&(i.scrollIntoView({behavior:"smooth",block:"start"}),t(!1))};return(0,a.jsx)(n.P.nav,{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(i?"bg-gray-900/95 backdrop-blur-sm shadow-lg":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.5},children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsx)(n.P.a,{href:"#home-section",className:"text-2xl font-bold text-white hover:text-blue-400 transition-colors",onClick:e=>{e.preventDefault(),d("#home-section")},whileHover:{scale:1.05},whileTap:{scale:.95},children:"RR"}),(0,a.jsx)("div",{className:"hidden md:flex space-x-8",children:c.map((e,t)=>(0,a.jsx)(n.P.a,{href:e.href,className:"text-white hover:text-blue-400 transition-colors font-medium",onClick:t=>{t.preventDefault(),d(e.href)},initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},whileHover:{scale:1.05},children:e.label},e.href))}),(0,a.jsx)("button",{className:"md:hidden text-white p-2",onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?(0,a.jsx)(r.A,{size:24}):(0,a.jsx)(l.A,{size:24})})]}),(0,a.jsx)(n.P.div,{className:"md:hidden ".concat(e?"block":"hidden"),initial:{opacity:0,height:0},animate:{opacity:+!!e,height:e?"auto":0},transition:{duration:.3},children:(0,a.jsx)("div",{className:"py-4 space-y-2",children:c.map(e=>(0,a.jsx)("a",{href:e.href,className:"block text-white hover:text-blue-400 transition-colors py-2 px-4 rounded",onClick:t=>{t.preventDefault(),d(e.href)},children:e.label},e.href))})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[566,441,684,358],()=>t(9600)),_N_E=e.O()}]);