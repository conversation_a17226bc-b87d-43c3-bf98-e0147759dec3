(()=>{var e={};e.id=974,e.ids=[974],e.modules={200:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\SkillsSection.tsx","default")},220:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});var n=i(687),r=i(1121),s=i(7472);let a=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:.6}}};return(0,n.jsx)("section",{id:"resume-section",className:"py-20 bg-white",ref:e,children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,n.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Life Experience"}),(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"Life Experience"}),(0,n.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"Experience is the most valuable lesson from God"})]}),(0,n.jsx)(r.P.div,{className:"grid md:grid-cols-2 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{period:"2014 - 2017",title:"SMAN 2 Tambun Selatan",position:"Highschool of Science",description:"The first experience when I'm falling in love with IT Things."},{period:"2017 - 2021",title:"University of Singaperbangsa Karawang",position:"Bachelor's Degree of Computer Science",description:"My first step to begin my journey to be a developer. I started my coding journey here and graduated with a cum laude predicate with a GPA of 3.71."},{period:"January - June (2020)",title:"PT Telekomunikasi Indonesia (WITEL Karawang)",position:"Chatbot Developer",description:"My first working experience as an intern. Here, I developed a chatbot on Telegram to analyze, monitor, and count the jobs already done by technicians."},{period:"January 2022 - February 2023",title:"Topkarir Indonesia",position:"Fullstack Web Developer",description:"My first Working Experience in the professional field was in Topkarir, a startup company in Human Resource field, in this place my responsibility is Develop and Maintenance Website, Frontend, Backend, API and connect to third party API for Topkarir Website and Microsite there are Jaknaker.id, Jatimcerdas, jabarjawara, TopKarir, AUA Topkarir, CDC BPSDMI. Topkarir. often using MySQL and PostgreSQL ,PHP, Javascript, Jquery, CSS, HTML and GO. often using framework Codeigniter and Laravel"},{period:"March 2023 - March 2024",title:"Katadata Indonesia",position:"Web Developer",description:"Responsible for developing, maintaining, and testing the Katadata website, API, and client-side applications using frameworks like Laravel, CodeIgniter, and React JS, with programming languages such as PHP and JavaScript. Also involved in data scraping using Python to support the data engineering team. Katadata is a startup in the online media industry."},{period:"July 2024 - Now",title:"RS Nasional Kanker Dharmais",position:"Programmer",description:"Developed and maintained hospital management software, optimizing workflows and enhancing patient data management. Collaborated with medical staff to create custom applications for scheduling, electronic medical records (EMR), and reporting tools. Ensured data security and compliance with health regulations by implementing robust cybersecurity measures. Improved system performance and user experience through continuous code optimization and feature updates."}].map((e,s)=>(0,n.jsx)(r.P.div,{className:"relative",variants:i,children:(0,n.jsxs)(r.P.div,{className:"bg-gray-50 p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300",whileHover:{y:-5},transition:{duration:.3},children:[(0,n.jsx)(r.P.span,{className:"inline-block bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium mb-4",initial:{scale:0},animate:t?{scale:1}:{scale:0},transition:{duration:.3,delay:.1*s},children:e.period}),(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:e.title}),(0,n.jsx)("h4",{className:"text-blue-600 font-semibold mb-3",children:e.position}),(0,n.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})},s))}),(0,n.jsx)(r.P.div,{className:"text-center mt-12",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.8},children:(0,n.jsx)(r.P.a,{href:"#",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Download CV"})})]})})}},474:(e,t,i)=>{"use strict";i.d(t,{default:()=>r.a});var n=i(1261),r=i.n(n)},512:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return f},defaultHead:function(){return d}});let n=i(4985),r=i(740),s=i(687),a=r._(i(3210)),o=n._(i(7755)),l=i(4959),u=i(9513),c=i(4604);function d(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(148);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(d(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,n={};return r=>{let s=!0,a=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){a=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?s=!1:t.add(r.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(r.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=r.props[t],i=n[t]||new Set;("name"!==t||!a)&&i.has(e)?s=!1:(i.add(e),n[t]=i)}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let f=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,c.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},554:(e,t)=>{"use strict";function i(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},587:(e,t,i)=>{"use strict";i.d(t,{default:()=>p});var n=i(687),r=i(1121),s=i(2688);let a=(0,s.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),o=(0,s.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),l=(0,s.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),u=(0,s.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var c=i(3872),d=i(1550);let h=(0,s.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),p=()=>{let e=new Date().getFullYear(),t=e=>{let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})};return(0,n.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,n.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,n.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"About"}),(0,n.jsx)("p",{className:"text-gray-400 mb-6 leading-relaxed",children:"Want to connect with me? feel free to follow my social media"}),(0,n.jsx)("div",{className:"flex space-x-4",children:[{icon:a,href:"https://twitter.com/rizqibennington",label:"Twitter"},{icon:o,href:"https://facebook.com/rizqirahmansyah10",label:"Facebook"},{icon:l,href:"https://instagram.com/rizqibennington",label:"Instagram"}].map((e,t)=>{let i=e.icon;return(0,n.jsx)(r.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-colors",whileHover:{scale:1.1,y:-2},whileTap:{scale:.95},initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},viewport:{once:!0},children:(0,n.jsx)(i,{size:18})},e.label)})})]}),(0,n.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Links"}),(0,n.jsx)("ul",{className:"space-y-3",children:[{href:"#home-section",label:"Home"},{href:"#about-section",label:"About"},{href:"#services-section",label:"Services"},{href:"#projects-section",label:"Projects"},{href:"#contact-section",label:"Contact"}].map((e,i)=>(0,n.jsx)(r.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.3,delay:.1*i},viewport:{once:!0},children:(0,n.jsxs)("a",{href:e.href,onClick:i=>{i.preventDefault(),t(e.href)},className:"text-gray-400 hover:text-white transition-colors flex items-center group",children:[(0,n.jsx)(u,{size:16,className:"mr-2 group-hover:translate-x-1 transition-transform"}),e.label]})},e.href))})]}),(0,n.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Services"}),(0,n.jsx)("ul",{className:"space-y-3",children:["Web Design","Web Development","Business Strategy","Data Analysis","Graphic Design"].map((e,i)=>(0,n.jsx)(r.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.3,delay:.1*i},viewport:{once:!0},children:(0,n.jsxs)("a",{href:"#services-section",onClick:e=>{e.preventDefault(),t("#services-section")},className:"text-gray-400 hover:text-white transition-colors flex items-center group",children:[(0,n.jsx)(u,{size:16,className:"mr-2 group-hover:translate-x-1 transition-transform"}),e]})},e))})]}),(0,n.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},viewport:{once:!0},children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Have a Questions?"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(r.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-gray-400 hover:text-white transition-colors group",whileHover:{x:5},children:[(0,n.jsx)(c.A,{size:20,className:"mr-3 text-green-500"}),(0,n.jsx)("span",{children:"+6281293062103"})]}),(0,n.jsxs)(r.P.a,{href:"mailto:<EMAIL>?subject=Need%20Help&body=Hi%20Rizqi,%20i%20need%20your%20help",className:"flex items-center text-gray-400 hover:text-white transition-colors group",whileHover:{x:5},children:[(0,n.jsx)(d.A,{size:20,className:"mr-3 text-blue-500"}),(0,n.jsx)("span",{children:"<EMAIL>"})]})]})]})]}),(0,n.jsx)(r.P.div,{className:"border-t border-gray-800 mt-12 pt-8 text-center",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.8},viewport:{once:!0},children:(0,n.jsxs)("p",{className:"text-gray-400",children:["Copyright \xa9 ",e," All rights reserved | This template is made with"," ",(0,n.jsx)(h,{size:16,className:"inline text-red-500 mx-1"})," by"," ",(0,n.jsx)("a",{href:"https://colorlib.com",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"Colorlib"})]})})]})})}},610:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\ServicesSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ServicesSection.tsx","default")},638:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});var n=i(687),r=i(1121),s=i(7472);let a=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,n.jsx)("section",{id:"skills-section",className:"py-20 bg-gray-50",ref:e,children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,n.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Skills"}),(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"My Skills"}),(0,n.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"here's some my major skill"})]}),(0,n.jsx)(r.P.div,{className:"grid md:grid-cols-2 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{name:"CSS",percentage:90,color:"bg-blue-500"},{name:"jQuery",percentage:85,color:"bg-green-500"},{name:"HTML5",percentage:95,color:"bg-orange-500"},{name:"PHP",percentage:90,color:"bg-purple-500"},{name:"Javascript",percentage:70,color:"bg-yellow-500"},{name:"Python",percentage:80,color:"bg-red-500"}].map((e,s)=>(0,n.jsxs)(r.P.div,{className:"bg-white p-6 rounded-lg shadow-lg",variants:i,whileHover:{y:-5},transition:{duration:.3},children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:e.name}),(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-600",children:[e.percentage,"%"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:(0,n.jsx)(r.P.div,{className:`h-full ${e.color} rounded-full relative`,initial:{width:0},animate:t?{width:`${e.percentage}%`}:{width:0},transition:{duration:1.5,delay:.2*s,ease:"easeOut"},children:(0,n.jsx)(r.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent",initial:{x:"-100%"},animate:t?{x:"100%"}:{x:"-100%"},transition:{duration:1.5,delay:.2*s+.5,ease:"easeInOut"}})})})]},e.name))}),(0,n.jsxs)(r.P.div,{className:"mt-16 text-center",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:1},children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Other Technologies"}),(0,n.jsx)("div",{className:"flex flex-wrap justify-center gap-3",children:["React","Angular","Node.js","MySQL","PostgreSQL","Laravel","CodeIgniter","Git","Docker","AWS"].map((e,i)=>(0,n.jsx)(r.P.span,{className:"bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",initial:{opacity:0,scale:0},animate:t?{opacity:1,scale:1}:{opacity:0,scale:0},transition:{duration:.3,delay:1.2+.1*i,ease:"easeOut"},whileHover:{scale:1.1},children:e},e))})]})]})})}},660:(e,t)=>{"use strict";function i(e){let t=5381;for(let i=0;i<e.length;i++)t=(t<<5)+t+e.charCodeAt(i)|0;return t>>>0}function n(e){return i(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{djb2Hash:function(){return i},hexHash:function(){return n}})},803:(e,t,i)=>{"use strict";i.d(t,{default:()=>l});var n=i(687),r=i(1121),s=i(7472),a=i(474);let o=(0,i(2688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),l=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,n.jsx)("section",{id:"projects-section",className:"py-20 bg-gray-50",ref:e,children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,n.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Projects"}),(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"My Projects"}),(0,n.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"Here's Some of My Project that already done"})]}),(0,n.jsx)(r.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{title:"Katadata ESG Index",subtitle:"Katadata Green",image:"/images/katadata-esg-index.png",description:"Environmental, Social, and Governance index platform for sustainable business evaluation.",link:"#"},{title:"Sistem Informasi Komite Etik dan Hukum Dharmais",subtitle:"SIKETIK",image:"/images/siketik.png",description:"Ethics and Legal Committee Information System for hospital management.",link:"#"},{title:"Indeks Saham",subtitle:"Databoks Katadata",image:"/images/marketdata.png",description:"Stock market index tracking and analysis platform.",link:"#"},{title:"Analisis Sentimen Debat Pilpres 2024",subtitle:"Katadata Pemilu 2024",image:"/images/analisis-sentimen.png",description:"Sentiment analysis platform for 2024 presidential election debates.",link:"#"},{title:"Career Development Center BPSDMI",subtitle:"TopKarir x Kemenperin",image:"/images/bpsdmi.png",description:"Career development platform for industrial workforce.",link:"#"},{title:"Jabar Jawara Career Center",subtitle:"Disnakertrans Jabar X TopKarir",image:"/images/jabarjawara.png",description:"West Java career center platform for job seekers and employers.",link:"#"}].map(e=>(0,n.jsxs)(r.P.div,{className:"group relative overflow-hidden rounded-lg shadow-lg bg-white",variants:i,whileHover:{y:-5},transition:{duration:.3},children:[(0,n.jsxs)("div",{className:"relative h-64 overflow-hidden",children:[(0,n.jsx)(a.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-110"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,n.jsxs)("div",{className:"absolute bottom-4 left-4 right-4 text-white",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-1 line-clamp-2",children:e.title}),(0,n.jsx)("p",{className:"text-sm text-gray-300 mb-3",children:e.subtitle}),(0,n.jsx)("p",{className:"text-sm text-gray-200 line-clamp-2 mb-3",children:e.description}),(0,n.jsxs)(r.P.a,{href:e.link,className:"inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:["View Project",(0,n.jsx)(o,{size:16})]})]})})]}),(0,n.jsxs)("div",{className:"p-6 md:hidden",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.title}),(0,n.jsx)("p",{className:"text-sm text-blue-600 mb-2",children:e.subtitle}),(0,n.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,n.jsxs)("a",{href:e.link,className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-sm",children:["View Project",(0,n.jsx)(o,{size:16})]})]})]},e.title))}),(0,n.jsx)(r.P.div,{className:"text-center mt-12",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:1},children:(0,n.jsx)(r.P.a,{href:"#contact-section",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:e=>{e.preventDefault(),document.querySelector("#contact-section")?.scrollIntoView({behavior:"smooth",block:"start"})},children:"Let's Work Together"})})]})})}},843:(e,t,i)=>{"use strict";i.d(t,{default:()=>u});var n=i(687),r=i(1121),s=i(7472);let a=(0,i(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var o=i(3872),l=i(1550);let u=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),i=[{icon:a,title:"Address",content:"Bekasi, West Java 17520",link:null},{icon:o.A,title:"Contact Number",content:"+6281293062103",link:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help"},{icon:l.A,title:"Email Address",content:"<EMAIL>",link:"mailto:<EMAIL>?subject=Need%20Help&body=Hi%20Rizqi,%20i%20need%20your%20help"}],u={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,n.jsx)("section",{id:"contact-section",className:"py-20 bg-white",ref:e,children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,n.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Contact"}),(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"Contact Me"}),(0,n.jsx)("p",{className:"text-gray-600 mt-4 text-lg",children:"Need some help with your website? feel free to contact me! :)"})]}),(0,n.jsx)(r.P.div,{className:"grid md:grid-cols-3 gap-8 mb-16",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:i.map(e=>{let t=e.icon;return(0,n.jsx)(r.P.div,{className:"text-center",variants:u,children:(0,n.jsxs)(r.P.div,{className:"bg-gray-50 hover:bg-blue-50 p-8 rounded-lg transition-all duration-300",whileHover:{y:-5,boxShadow:"0 10px 30px rgba(0,0,0,0.1)"},transition:{duration:.3},children:[(0,n.jsx)(r.P.div,{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6",whileHover:{scale:1.1,rotate:5},transition:{duration:.3},children:(0,n.jsx)(t,{size:32,className:"text-blue-600"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:e.title}),e.link?(0,n.jsx)(r.P.a,{href:e.link,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-700 transition-colors",whileHover:{scale:1.05},children:e.content}):(0,n.jsx)("p",{className:"text-gray-600",children:e.content})]})},e.title)})}),(0,n.jsxs)(r.P.div,{className:"relative bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg overflow-hidden",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.8},children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-cover bg-center opacity-20",style:{backgroundImage:"url(/images/bg_1.jpg)"}}),(0,n.jsxs)("div",{className:"relative z-10 text-center py-16 px-8",children:[(0,n.jsxs)(r.P.h2,{className:"text-3xl md:text-4xl font-bold text-white mb-4",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:1},children:["I'm ",(0,n.jsx)("span",{className:"text-yellow-300",children:"Available"})," for freelancing"]}),(0,n.jsx)(r.P.p,{className:"text-xl text-blue-100 mb-8",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:1.2},children:"Feel Free to contact me"}),(0,n.jsx)(r.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.6,delay:1.4},whileHover:{scale:1.05},whileTap:{scale:.95},children:"Contact Me Now"})]})]})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1018:(e,t,i)=>{"use strict";i.d(t,{default:()=>l});var n=i(687),r=i(1121),s=i(7472),a=i(474),o=i(3210);let l=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),[i,l]=(0,o.useState)(0);(0,o.useEffect)(()=>{if(t){let e=0,t=setInterval(()=>{(e+=5.84)>=730?(l(730),clearInterval(t)):l(Math.floor(e))},16);return()=>clearInterval(t)}},[t]);let u={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},c={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,n.jsx)("section",{id:"about-section",className:"py-20 bg-gray-50",ref:e,children:(0,n.jsx)("div",{className:"container mx-auto px-4",children:(0,n.jsxs)(r.P.div,{className:"grid md:grid-cols-2 gap-12 items-center",variants:u,initial:"hidden",animate:t?"visible":"hidden",children:[(0,n.jsx)(r.P.div,{className:"relative",variants:c,children:(0,n.jsxs)("div",{className:"relative w-full max-w-md mx-auto",children:[(0,n.jsx)(r.P.div,{whileHover:{scale:1.02},transition:{duration:.3},children:(0,n.jsx)(a.default,{src:"/images/aboutme.png",alt:"About Rizqi Rahmansyah",width:400,height:500,className:"w-full h-auto rounded-lg shadow-xl"})}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-blue-600/20 to-transparent rounded-lg"})]})}),(0,n.jsxs)(r.P.div,{className:"space-y-6",variants:c,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.P.h1,{className:"text-6xl font-bold text-gray-200 mb-2",variants:c,children:"About"}),(0,n.jsx)(r.P.h2,{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",variants:c,children:"About Me"})]}),(0,n.jsx)(r.P.p,{className:"text-gray-600 leading-relaxed text-lg",variants:c,children:"My journey into web development started with a fascination for how the web works, leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as React and Angular on the front end. On the server side, I'm skilled in PHP, Node.js, and MySQL, with a particular focus on building scalable and secure web applications."}),(0,n.jsx)(r.P.ul,{className:"space-y-3",variants:u,children:[{label:"Name:",value:"Rizqi Rahmansyah"},{label:"Date of birth:",value:"December 10, 1998"},{label:"Address:",value:"Bekasi"},{label:"Phone:",value:"+6281293062103"}].map((e,t)=>(0,n.jsxs)(r.P.li,{className:"flex text-gray-700",variants:c,children:[(0,n.jsx)("span",{className:"font-semibold w-32",children:e.label}),(0,n.jsx)("span",{children:e.value})]},t))}),(0,n.jsxs)(r.P.div,{className:"bg-white p-6 rounded-lg shadow-lg",variants:c,children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(r.P.div,{className:"text-4xl font-bold text-blue-600 mb-2",initial:{scale:0},animate:t?{scale:1}:{scale:0},transition:{duration:.5,delay:.5},children:i}),(0,n.jsx)("div",{className:"text-gray-600 font-medium",children:"Days Working Experience"})]}),(0,n.jsx)(r.P.div,{className:"mt-6",variants:c,children:(0,n.jsx)(r.P.a,{href:"#",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Download CV"})})]})]})]})})})}},1121:(e,t,i)=>{"use strict";let n;function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function s(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function a(e,t,i,n){if("function"==typeof t){let[r,a]=s(n);t=t(void 0!==i?i:e.custom,r,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,a]=s(n);t=t(void 0!==i?i:e.custom,r,a)}return t}function o(e,t,i){let n=e.getProps();return a(n,t,void 0!==i?i:n.custom,e)}function l(e,t){return e?.[t]??e?.default??e}i.d(t,{P:()=>sS});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function p(e,t){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=d.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,n=new Set,r=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,s=!1)=>{let o=s&&r?i:n;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{n.delete(e),a.delete(e)},process:e=>{if(o=e,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,i.clear(),r=!1,s&&(s=!1,c.process(e))}};return c}(s,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=a,v=()=>{let s=c.useManualTiming?r.timestamp:performance.now();i=!1,c.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,o.process(r),l.process(r),u.process(r),p.process(r),m.process(r),f.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&t&&(n=!1,e(v))},x=()=>{i=!0,n=!0,r.isProcessing||e(v)};return{schedule:d.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,r=!1)=>(i||x(),n.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)a[d[t]].cancel(e)},state:r,steps:a}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function P(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class j{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>P(this.subscriptions,e)}notify(e,t,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function E(){n=void 0}let S={now:()=>(void 0===n&&S.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(E)}},T=e=>!isNaN(parseFloat(e)),A={current:void 0};class C{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=S.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=S.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=T(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new j);let i=this.events[e].add(t);return"change"===e?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=S.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function R(e,t){return new C(e,t)}let k=e=>Array.isArray(e),M=e=>!!(e&&e.getVelocity);function N(e,t){let i=e.getValue("willChange");if(M(i)&&i.add)return i.add(t);if(!i&&c.WillChange){let i=new c.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let _=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+_("framerAppearId"),D=(e,t)=>i=>t(e(i)),V=(...e)=>e.reduce(D),I=(e,t,i)=>i>t?t:i<e?e:i,L=e=>1e3*e,z=e=>e/1e3,F={layout:0,mainThread:0,waapi:0},U=()=>{},$=()=>{},B=e=>t=>"string"==typeof t&&t.startsWith(e),q=B("--"),H=B("var(--"),W=e=>!!H(e)&&X.test(e.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},G={...K,transform:e=>I(0,1,e)},Y={...K,default:1},J=e=>Math.round(1e5*e)/1e5,Z=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>i=>!!("string"==typeof i&&Q.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),et=(e,t,i)=>n=>{if("string"!=typeof n)return n;let[r,s,a,o]=n.match(Z);return{[e]:parseFloat(r),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ei=e=>I(0,255,e),en={...K,transform:e=>Math.round(ei(e))},er={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:n=1})=>"rgba("+en.transform(e)+", "+en.transform(t)+", "+en.transform(i)+", "+J(G.transform(n))+")"},es={test:ee("#"),parse:function(e){let t="",i="",n="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),n=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),n=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,n+=n,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:er.transform},ea=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eo=ea("deg"),el=ea("%"),eu=ea("px"),ec=ea("vh"),ed=ea("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ep={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:n=1})=>"hsla("+Math.round(e)+", "+el.transform(J(t))+", "+el.transform(J(i))+", "+J(G.transform(n))+")"},em={test:e=>er.test(e)||es.test(e)||ep.test(e),parse:e=>er.test(e)?er.parse(e):ep.test(e)?ep.parse(e):es.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?er.transform(e):ep.transform(e)},ef=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ex(e){let t=e.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,a=t.replace(ev,e=>(em.test(e)?(n.color.push(s),r.push(ey),i.push(em.parse(e))):e.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(e)):(n.number.push(s),r.push(eg),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:n,types:r}}function eb(e){return ex(e).values}function ew(e){let{split:t,types:i}=ex(e),n=t.length;return e=>{let r="";for(let s=0;s<n;s++)if(r+=t[s],void 0!==e[s]){let t=i[s];t===eg?r+=J(e[s]):t===ey?r+=em.transform(e[s]):r+=e[s]}return r}}let eP=e=>"number"==typeof e?0:e,ej={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Z)?.length||0)+(e.match(ef)?.length||0)>0},parse:eb,createTransformer:ew,getAnimatableNone:function(e){let t=eb(e);return ew(e)(t.map(eP))}};function eE(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function eS(e,t){return i=>i>0?t:e}let eT=(e,t,i)=>e+(t-e)*i,eA=(e,t,i)=>{let n=e*e,r=i*(t*t-n)+n;return r<0?0:Math.sqrt(r)},eC=[es,er,ep],eR=e=>eC.find(t=>t.test(e));function ek(e){let t=eR(e);if(U(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===ep&&(i=function({hue:e,saturation:t,lightness:i,alpha:n}){e/=360,i/=100;let r=0,s=0,a=0;if(t/=100){let n=i<.5?i*(1+t):i+t-i*t,o=2*i-n;r=eE(o,n,e+1/3),s=eE(o,n,e),a=eE(o,n,e-1/3)}else r=s=a=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(i)),i}let eM=(e,t)=>{let i=ek(e),n=ek(t);if(!i||!n)return eS(e,t);let r={...i};return e=>(r.red=eA(i.red,n.red,e),r.green=eA(i.green,n.green,e),r.blue=eA(i.blue,n.blue,e),r.alpha=eT(i.alpha,n.alpha,e),er.transform(r))},eN=new Set(["none","hidden"]);function e_(e,t){return i=>eT(e,t,i)}function eO(e){return"number"==typeof e?e_:"string"==typeof e?W(e)?eS:em.test(e)?eM:eI:Array.isArray(e)?eD:"object"==typeof e?em.test(e)?eM:eV:eS}function eD(e,t){let i=[...e],n=i.length,r=e.map((e,i)=>eO(e)(e,t[i]));return e=>{for(let t=0;t<n;t++)i[t]=r[t](e);return i}}function eV(e,t){let i={...e,...t},n={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(n[r]=eO(e[r])(e[r],t[r]));return e=>{for(let t in n)i[t]=n[t](e);return i}}let eI=(e,t)=>{let i=ej.createTransformer(t),n=ex(e),r=ex(t);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?eN.has(e)&&!r.values.length||eN.has(t)&&!n.values.length?function(e,t){return eN.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):V(eD(function(e,t){let i=[],n={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let s=t.types[r],a=e.indexes[s][n[s]],o=e.values[a]??0;i[r]=o,n[s]++}return i}(n,r),r.values),i):(U(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eS(e,t))};function eL(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?eT(e,t,i):eO(e)(e,t)}let ez=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>m.update(t,e),stop:()=>f(t),now:()=>g.isProcessing?g.timestamp:S.now()}},eF=(e,t,i=10)=>{let n="",r=Math.max(Math.round(t/i),2);for(let t=0;t<r;t++)n+=e(t/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function eU(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function e$(e,t,i){var n,r;let s=Math.max(t-5,0);return n=i-e(s),(r=t-s)?1e3/r*n:0}let eB={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eq(e,t){return e*Math.sqrt(1-t*t)}let eH=["duration","bounce"],eW=["stiffness","damping","mass"];function eX(e,t){return t.some(t=>void 0!==e[t])}function eK(e=eB.visualDuration,t=eB.bounce){let i,n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:s}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:eB.velocity,stiffness:eB.stiffness,damping:eB.damping,mass:eB.mass,isResolvedFromDuration:!1,...e};if(!eX(e,eW)&&eX(e,eH))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),n=i*i,r=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:eB.mass,stiffness:n,damping:r}}else{let i=function({duration:e=eB.duration,bounce:t=eB.bounce,velocity:i=eB.velocity,mass:n=eB.mass}){let r,s;U(e<=L(eB.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=I(eB.minDamping,eB.maxDamping,a),e=I(eB.minDuration,eB.maxDuration,z(e)),a<1?(r=t=>{let n=t*a,r=n*e;return .001-(n-i)/eq(t,a)*Math.exp(-r)},s=t=>{let n=t*a*e,s=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-n),l=eq(Math.pow(t,2),a);return(n*i+i-s)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),s=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let n=i;for(let i=1;i<12;i++)n-=e(n)/t(n);return n}(r,s,5/e);if(e=L(e),isNaN(o))return{stiffness:eB.stiffness,damping:eB.damping,duration:e};{let t=Math.pow(o,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...i,mass:eB.mass}).isResolvedFromDuration=!0}return t}({...n,velocity:-z(n.velocity||0)}),f=p||0,g=c/(2*Math.sqrt(u*d)),y=o-a,v=z(Math.sqrt(u/d)),x=5>Math.abs(y);if(r||(r=x?eB.restSpeed.granular:eB.restSpeed.default),s||(s=x?eB.restDelta.granular:eB.restDelta.default),g<1){let e=eq(v,g);i=t=>o-Math.exp(-g*v*t)*((f+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)i=e=>o-Math.exp(-v*e)*(y+(f+v*y)*e);else{let e=v*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*v*t),n=Math.min(e*t,300);return o-i*((f+g*v*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}let b={calculatedDuration:m&&h||null,next:e=>{let t=i(e);if(m)l.done=e>=h;else{let n=0===e?f:0;g<1&&(n=0===e?L(f):e$(i,e,t));let a=Math.abs(o-t)<=s;l.done=Math.abs(n)<=r&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eU(b),2e4),t=eF(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eG({keyframes:e,velocity:t=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=i*t,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let b=e=>-y*Math.exp(-e/n),w=e=>x+b(e),P=e=>{let t=b(e),i=w(e);m.done=Math.abs(t)<=u,m.value=m.done?x:i},j=e=>{f(m.value)&&(d=e,h=eK({keyframes:[m.value,g(m.value)],velocity:e$(w,e,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,P(e),j(e)),void 0!==d&&e>=d)?h.next(e-d):(t||P(e),m)}}}eK.applyToOptions=e=>{let t=function(e,t=100,i){let n=i({...e,keyframes:[0,t]}),r=Math.min(eU(n),2e4);return{type:"keyframes",ease:e=>n.next(r*e).value/t,duration:z(r)}}(e,100,eK);return e.ease=t.ease,e.duration=L(t.duration),e.type="keyframes",e};let eY=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function eJ(e,t,i,n){if(e===t&&i===n)return u;let r=t=>(function(e,t,i,n,r){let s,a,o=0;do(s=eY(a=t+(i-t)/2,n,r)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:eY(r(e),t,n)}let eZ=eJ(.42,0,1,1),eQ=eJ(0,0,.58,1),e0=eJ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e3=e=>t=>1-e(1-t),e5=eJ(.33,1.53,.69,.99),e6=e3(e5),e4=e2(e6),e8=e=>(e*=2)<1?.5*e6(e):.5*(2-Math.pow(2,-10*(e-1))),e9=e=>1-Math.sin(Math.acos(e)),e7=e3(e9),te=e2(e9),tt=e=>Array.isArray(e)&&"number"==typeof e[0],ti={linear:u,easeIn:eZ,easeInOut:e0,easeOut:eQ,circIn:e9,circInOut:te,circOut:e7,backIn:e6,backInOut:e4,backOut:e5,anticipate:e8},tn=e=>"string"==typeof e,tr=e=>{if(tt(e)){$(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,n,r]=e;return eJ(t,i,n,r)}return tn(e)?($(void 0!==ti[e],`Invalid easing type '${e}'`),ti[e]):e},ts=(e,t,i)=>{let n=t-e;return 0===n?1:(i-e)/n};function ta({duration:e=300,keyframes:t,times:i,ease:n="easeInOut"}){var r;let s=e1(n)?n.map(tr):tr(n),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:n,mixer:r}={}){let s=e.length;if($(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let n=[],r=i||c.mix||eL,s=e.length-1;for(let i=0;i<s;i++){let s=r(e[i],e[i+1]);t&&(s=V(Array.isArray(t)?t[i]||u:t,s)),n.push(s)}return n}(t,n,r),l=o.length,d=i=>{if(a&&i<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(i<e[n+1]);n++);let r=ts(e[n],e[n+1],i);return o[n](r)};return i?t=>d(I(e[0],e[s-1],t)):d}((r=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let n=1;n<=t;n++){let r=ts(0,t,n);e.push(eT(i,1,r))}}(t,e.length-1),t}(t),r.map(t=>t*e)),t,{ease:Array.isArray(s)?s:t.map(()=>s||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let to=e=>null!==e;function tl(e,{repeat:t,repeatType:i="loop"},n,r=1){let s=e.filter(to),a=r<0||t&&"loop"!==i&&t%2==1?0:s.length-1;return a&&void 0!==n?n:s[a]}let tu={decay:eG,inertia:eG,tween:ta,keyframes:ta,spring:eK};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tp extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==S.now()&&this.tick(S.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ta,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=e,{keyframes:a}=e,o=t||ta;o!==ta&&"number"!=typeof a[0]&&(this.mixKeyframes=V(th,eL(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=eU(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(c){let e=Math.min(this.currentTime,n)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(i=1-i,h&&(i-=h/a)):"mirror"===d&&(x=s)),v=I(0,1,i)*a}let b=y?{done:!1,value:u[0]}:x.next(v);r&&(b.value=r(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&p!==eG&&(b.value=tl(u,this.options,f,this.speed)),m&&m(b.value),P&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return z(this.calculatedDuration)}get time(){return z(this.currentTime)}set time(e){e=L(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(S.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=z(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=ez,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(S.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tm=e=>180*e/Math.PI,tf=e=>ty(tm(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tf,rotateZ:tf,skewX:e=>tm(Math.atan(e[1])),skewY:e=>tm(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tx=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tx,scale:e=>(tv(e)+tx(e))/2,rotateX:e=>ty(tm(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tm(Math.atan2(-e[2],e[0]))),rotateZ:tf,rotate:tf,skewX:e=>tm(Math.atan(e[4])),skewY:e=>tm(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tP(e,t){let i,n;if(!e||"none"===e)return tw(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=tb,n=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tg,n=t}if(!n)return tw(t);let s=i[t],a=n[1].split(",").map(tE);return"function"==typeof s?s(a):a[s]}let tj=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return tP(i,t)};function tE(e){return parseFloat(e.trim())}let tS=e=>e===K||e===eu,tT=new Set(["x","y","z"]),tA=v.filter(e=>!tT.has(e)),tC={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tP(t,"x"),y:(e,{transform:t})=>tP(t,"y")};tC.translateX=tC.x,tC.translateY=tC.y;let tR=new Set,tk=!1,tM=!1,tN=!1;function t_(){if(tM){let e=Array.from(tR).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(i=>{let n=e.getValue(i);void 0!==n&&(t.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tM=!1,tk=!1,tR.forEach(e=>e.complete(tN)),tR.clear()}function tO(){tR.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tM=!0)})}class tD{constructor(e,t,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tR.add(this),tk||(tk=!0,m.read(tO),m.resolveKeyframes(t_))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:n}=this;if(null===e[0]){let r=n?.get(),s=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let n=i.readValue(t,s);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=s),n&&void 0===r&&n.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tR.delete(this)}cancel(){"scheduled"===this.state&&(tR.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tV=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tL=tI(()=>void 0!==window.ScrollTimeline),tz={},tF=function(e,t){let i=tI(e);return()=>tz[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tU=([e,t,i,n])=>`cubic-bezier(${e}, ${t}, ${i}, ${n})`,t$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tU([0,.65,.55,1]),circOut:tU([.55,0,1,.45]),backIn:tU([.31,.01,.66,-.59]),backOut:tU([.33,1.53,.69,.99])};function tB(e){return"function"==typeof e&&"applyToOptions"in e}class tq extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=e,$("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tB(e)&&tF()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let c={[t]:i};l&&(c.offset=l);let d=function e(t,i){if(t)return"function"==typeof t?tF()?eF(t,i):"ease-out":tt(t)?tU(t):Array.isArray(t)?t.map(t=>e(t,i)||t$.easeOut):t$[t]}(o,r);Array.isArray(d)&&(c.easing=d),h.value&&F.waapi++;let p={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(p.pseudoElement=u);let m=e.animate(c,p);return h.value&&m.finished.finally(()=>{F.waapi--}),m}(t,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=tl(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){tV(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return z(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return z(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=L(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tL())?(this.animation.timeline=e,u):t(this)}}let tH={anticipate:e8,backInOut:e4,circInOut:te};class tW extends tq{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tH&&(e.ease=tH[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new tp({...s,autoplay:!1}),o=L(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let tX=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(ej.test(e)||"0"===e)&&!e.startsWith("url("));function tK(e){return"object"==typeof e&&null!==e}function tG(e){return tK(e)&&"offsetHeight"in e}let tY=new Set(["opacity","clipPath","filter","transform"]),tJ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tZ extends td{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=S.now();let d={autoplay:e,delay:t,type:i,repeat:n,repeatDelay:r,repeatType:s,name:o,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tD;this.keyframeResolver=new h(a,(e,t,i)=>this.onKeyframesResolved(e,t,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:d}=i;this.resolvedAt=S.now(),!function(e,t,i,n){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=tX(r,t),o=tX(s,t);return U(a===o,`You are trying to animate ${t} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||tB(i))&&n)}(e,r,s,a)&&((c.instantAnimations||!o)&&d?.(tl(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},p=!l&&function(e){let{motionValue:t,name:i,repeatDelay:n,repeatType:r,damping:s,type:a}=e;if(!tG(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tJ()&&i&&tY.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==r&&0!==s&&"inertia"!==a}(h)?new tW({...h,element:h.motionValue.owner.current}):new tp(h);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tN=!0,tO(),t_(),tN=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tQ=e=>null!==e,t0={type:"spring",stiffness:500,damping:25,restSpeed:10},t1=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t2={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t5=(e,{keyframes:t})=>t.length>2?t2:x.has(e)?e.startsWith("scale")?t1(t[1]):t0:t3,t6=(e,t,i,n={},r,s)=>a=>{let o=l(n,e)||{},u=o.delay||n.delay||0,{elapsed:d=0}=n;d-=L(u);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:s?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&Object.assign(h,t5(e,h)),h.duration&&(h.duration=L(h.duration)),h.repeatDelay&&(h.repeatDelay=L(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(p=!0)),(c.instantAnimations||c.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,p&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},n){let r=e.filter(tQ),s=t&&"loop"!==i&&t%2==1?0:r.length-1;return r[s]}(h.keyframes,o);if(void 0!==e)return void m.update(()=>{h.onUpdate(e),h.onComplete()})}return o.isSync?new tp(h):new tZ(h)};function t4(e,t,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:a,...u}=t;n&&(s=n);let c=[],d=r&&e.animationState&&e.animationState.getState()[r];for(let t in u){let n=e.getValue(t,e.latestValues[t]??null),r=u[t];if(void 0===r||d&&function({protectedKeys:e,needsAnimating:t},i){let n=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,n}(d,t))continue;let a={delay:i,...l(s||{},t)},o=n.get();if(void 0!==o&&!n.isAnimating&&!Array.isArray(r)&&r===o&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[O];if(i){let e=window.MotionHandoffAnimation(i,t,m);null!==e&&(a.startTime=e,h=!0)}}N(e,t),n.start(t6(t,n,r,e.shouldReduceMotion&&b.has(t)?{type:!1}:a,e,h));let p=n.animation;p&&c.push(p)}return a&&Promise.all(c).then(()=>{m.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:n={},...r}=o(e,t)||{};for(let t in r={...r,...i}){var s;let i=k(s=r[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,R(i))}}(e,a)})}),c}function t8(e,t,i={}){let n=o(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(t4(e,n,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,i=0,n=0,r=1,s){let a=[],o=(e.variantChildren.size-1)*n,l=1===r?(e=0)=>e*n:(e=0)=>o-e*n;return Array.from(e.variantChildren).sort(t9).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(t8(e,t,{...s,delay:i+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,s+n,a,o,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),a(i.delay)]);{let[e,t]="beforeChildren"===l?[s,a]:[a,s];return e().then(()=>t())}}function t9(e,t){return e.sortNodePosition(t)}function t7(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let n=0;n<i;n++)if(t[n]!==e[n])return!1;return!0}function ie(e){return"string"==typeof e||Array.isArray(e)}let it=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ii=["initial",...it],ir=ii.length,is=[...it].reverse(),ia=it.length;function io(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function il(){return{animate:io(!0),whileInView:io(),whileHover:io(),whileTap:io(),whileDrag:io(),whileFocus:io(),exit:io()}}class iu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ic extends iu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>t8(e,t,i)));else if("string"==typeof t)n=t8(e,t,i);else{let r="function"==typeof t?o(e,t,i.custom):t;n=Promise.all(t4(e,r,i))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=il(),n=!0,s=t=>(i,n)=>{let r=o(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...n}=r;i={...i,...n,...t}}return i};function a(a){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<ir;e++){let n=ii[e],r=t.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(e.parent)||{},c=[],d=new Set,h={},p=1/0;for(let t=0;t<ia;t++){var m,f;let o=is[t],g=i[o],y=void 0!==l[o]?l[o]:u[o],v=ie(y),x=o===a?g.isActive:null;!1===x&&(p=t);let b=y===u[o]&&y!==l[o]&&v;if(b&&n&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...h},!g.isActive&&null===x||!y&&!g.prevProp||r(y)||"boolean"==typeof y)continue;let w=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!t7(f,m)),P=w||o===a&&g.isActive&&!b&&v||t>p&&v,j=!1,E=Array.isArray(y)?y:[y],S=E.reduce(s(o),{});!1===x&&(S={});let{prevResolvedValues:T={}}=g,A={...T,...S},C=t=>{P=!0,d.has(t)&&(j=!0,d.delete(t)),g.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in A){let t=S[e],i=T[e];if(h.hasOwnProperty(e))continue;let n=!1;(k(t)&&k(i)?t7(t,i):t===i)?void 0!==t&&d.has(e)?C(e):g.protectedKeys[e]=!0:null!=t?C(e):d.add(e)}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(h={...h,...S}),n&&e.blockInitialAnimation&&(P=!1);let R=!(b&&w)||j;P&&R&&c.push(...E.map(e=>({animation:e,options:{type:o}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let i=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(t.transition=i.transition)}d.forEach(i=>{let n=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=n??null}),c.push({animation:t})}let g=!!c.length;return n&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(c):Promise.resolve()}return{animateChanges:a,setActive:function(t,n){if(i[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),i[t].isActive=n;let r=a(t);for(let e in i)i[e].protectedKeys={};return r},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=il(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();r(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ih extends iu{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let ip={x:!1,y:!1};function im(e,t,i,n={passive:!0}){return e.addEventListener(t,i,n),()=>e.removeEventListener(t,i)}let ig=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function iy(e){return{point:{x:e.pageX,y:e.pageY}}}let iv=e=>t=>ig(t)&&e(t,iy(t));function ix(e,t,i,n){return im(e,t,iv(i),n)}function ib({top:e,left:t,right:i,bottom:n}){return{x:{min:t,max:i},y:{min:e,max:n}}}function iw(e){return e.max-e.min}function iP(e,t,i,n=.5){e.origin=n,e.originPoint=eT(t.min,t.max,e.origin),e.scale=iw(i)/iw(t),e.translate=eT(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function ij(e,t,i,n){iP(e.x,t.x,i.x,n?n.originX:void 0),iP(e.y,t.y,i.y,n?n.originY:void 0)}function iE(e,t,i){e.min=i.min+t.min,e.max=e.min+iw(t)}function iS(e,t,i){e.min=t.min-i.min,e.max=e.min+iw(t)}function iT(e,t,i){iS(e.x,t.x,i.x),iS(e.y,t.y,i.y)}let iA=()=>({translate:0,scale:1,origin:0,originPoint:0}),iC=()=>({x:iA(),y:iA()}),iR=()=>({min:0,max:0}),ik=()=>({x:iR(),y:iR()});function iM(e){return[e("x"),e("y")]}function iN(e){return void 0===e||1===e}function i_({scale:e,scaleX:t,scaleY:i}){return!iN(e)||!iN(t)||!iN(i)}function iO(e){return i_(e)||iD(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function iD(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iV(e,t,i,n,r){return void 0!==r&&(e=n+r*(e-n)),n+i*(e-n)+t}function iI(e,t=0,i=1,n,r){e.min=iV(e.min,t,i,n,r),e.max=iV(e.max,t,i,n,r)}function iL(e,{x:t,y:i}){iI(e.x,t.translate,t.scale,t.originPoint),iI(e.y,i.translate,i.scale,i.originPoint)}function iz(e,t){e.min=e.min+t,e.max=e.max+t}function iF(e,t,i,n,r=.5){let s=eT(e.min,e.max,r);iI(e,t,i,s,n)}function iU(e,t){iF(e.x,t.x,t.scaleX,t.scale,t.originX),iF(e.y,t.y,t.scaleY,t.scale,t.originY)}function i$(e,t){return ib(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let iB=({current:e})=>e?e.ownerDocument.defaultView:null;function iq(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let iH=(e,t)=>Math.abs(e-t);class iW{constructor(e,t,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iG(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iH(e.x,t.x)**2+iH(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:n}=e,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iX(t,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iG("pointercancel"===e.type?this.lastMoveEventInfo:iX(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),n&&n(e,s)},!ig(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.contextWindow=n||window;let s=iX(iy(e),this.transformPagePoint),{point:a}=s,{timestamp:o}=g;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iG(s,this.history)),this.removeListeners=V(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iX(e,t){return t?{point:t(e.point)}:e}function iK(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iG({point:e},t){return{point:e,delta:iK(e,iY(t)),offset:iK(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,n=null,r=iY(e);for(;i>=0&&(n=e[i],!(r.timestamp-n.timestamp>L(.1)));)i--;if(!n)return{x:0,y:0};let s=z(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let a={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function iY(e){return e[e.length-1]}function iJ(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function iZ(e,t){let i=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,n]=[n,i]),{min:i,max:n}}function iQ(e,t,i){return{min:i0(e,t),max:i0(e,i)}}function i0(e,t){return"number"==typeof e?e:e[t]||0}let i1=new WeakMap;class i2{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ik(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iW(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(iy(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(ip[e])return null;else return ip[e]=!0,()=>{ip[e]=!1};return ip.x||ip.y?null:(ip.x=ip.y=!0,()=>{ip.x=ip.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iM(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[e];n&&(t=iw(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&m.postRender(()=>r(e,t)),N(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>iM(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iB(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&m.postRender(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:n}=this.getProps();if(!i||!i3(e,n,this.currentDirection))return;let r=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:i},n){return void 0!==t&&e<t?e=n?eT(t,e,n.min):Math.max(e,t):void 0!==i&&e>i&&(e=n?eT(i,e,n.max):Math.min(e,i)),e}(s,this.constraints[e],this.elastic[e])),r.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&iq(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:n,right:r}){return{x:iJ(e.x,i,r),y:iJ(e.y,t,n)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:iQ(e,"left","right"),y:iQ(e,"top","bottom")}}(t),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iM(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iq(t))return!1;let n=t.current;$(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(e,t,i){let n=i$(e,i),{scroll:r}=t;return r&&(iz(n.x,r.offset.x),iz(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),a=(e=r.layout.layoutBox,{x:iZ(e.x,s.x),y:iZ(e.y,s.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=ib(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iM(a=>{if(!i3(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return N(this.visualElement,e),i.start(t6(e,i,0,t,this.visualElement,!1))}stopAnimation(){iM(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iM(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iM(t=>{let{drag:i}=this.getProps();if(!i3(t,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(t);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[t];r.set(e[t]-eT(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iq(t)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iM(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();n[e]=function(e,t){let i=.5,n=iw(e),r=iw(t);return r>n?i=ts(t.min,t.max-n,e.min):n>r&&(i=ts(e.min,e.max-r,t.min)),I(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iM(t=>{if(!i3(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:s}=this.constraints[t];i.set(eT(r,s,n[t]))})}addListeners(){if(!this.visualElement.current)return;i1.set(this.visualElement,this);let e=ix(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iq(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(t);let r=im(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iM(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),n(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:a}}}function i3(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class i5 extends iu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i2(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i6=e=>(t,i)=>{e&&m.postRender(()=>e(t,i))};class i4 extends iu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new iW(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iB(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i6(e),onStart:i6(t),onMove:i,onEnd:(e,t)=>{delete this.session,n&&m.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i8,i9,i7=i(687);let{schedule:ne}=p(queueMicrotask,!1);var nt=i(3210);let ni=(0,nt.createContext)(null),nn=(0,nt.createContext)({}),nr=(0,nt.createContext)({}),ns={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function na(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let no={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let i=na(e,t.target.x),n=na(e,t.target.y);return`${i}% ${n}%`}},nl={};class nu extends nt.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=e;for(let e in nd)nl[e]=nd[e],q(e)&&(nl[e].isCSSVariable=!0);r&&(t.group&&t.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),ns.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||e.layoutDependency!==t||void 0===t||e.isPresent!==r?s.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?s.promote():s.relegate()||m.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ne.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nc(e){let[t,i]=function(e=!0){let t=(0,nt.useContext)(ni);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=t,s=(0,nt.useId)();(0,nt.useEffect)(()=>{if(e)return r(s)},[e]);let a=(0,nt.useCallback)(()=>e&&n&&n(s),[s,n,e]);return!i&&n?[!1,a]:[!0]}(),n=(0,nt.useContext)(nn);return(0,i7.jsx)(nu,{...e,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nr),isPresent:t,safeToRemove:i})}let nd={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let n=ej.parse(e);if(n.length>5)return e;let r=ej.createTransformer(e),s=+("number"!=typeof n[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;n[0+s]/=a,n[1+s]/=o;let l=eT(a,o,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};function nh(e){return tK(e)&&"ownerSVGElement"in e}let np=(e,t)=>e.depth-t.depth;class nm{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){P(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(np),this.isDirty=!1,this.children.forEach(e)}}function nf(e){return M(e)?e.get():e}let ng=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=ng.length,nv=e=>"string"==typeof e?parseFloat(e):e,nx=e=>"number"==typeof e||eu.test(e);function nb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nw=nj(0,.5,e7),nP=nj(.5,.95,u);function nj(e,t,i){return n=>n<e?0:n>t?1:i(ts(e,t,n))}function nE(e,t){e.min=t.min,e.max=t.max}function nS(e,t){nE(e.x,t.x),nE(e.y,t.y)}function nT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nA(e,t,i,n,r){return e-=t,e=n+1/i*(e-n),void 0!==r&&(e=n+1/r*(e-n)),e}function nC(e,t,[i,n,r],s,a){!function(e,t=0,i=1,n=.5,r,s=e,a=e){if(el.test(t)&&(t=parseFloat(t),t=eT(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=eT(s.min,s.max,n);e===s&&(o-=t),e.min=nA(e.min,t,i,o,r),e.max=nA(e.max,t,i,o,r)}(e,t[i],t[n],t[r],t.scale,s,a)}let nR=["x","scaleX","originX"],nk=["y","scaleY","originY"];function nM(e,t,i,n){nC(e.x,t,nR,i?i.x:void 0,n?n.x:void 0),nC(e.y,t,nk,i?i.y:void 0,n?n.y:void 0)}function nN(e){return 0===e.translate&&1===e.scale}function n_(e){return nN(e.x)&&nN(e.y)}function nO(e,t){return e.min===t.min&&e.max===t.max}function nD(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nV(e,t){return nD(e.x,t.x)&&nD(e.y,t.y)}function nI(e){return iw(e.x)/iw(e.y)}function nL(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nz{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(P(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nF={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nU=["","X","Y","Z"],n$={visibility:"hidden"},nB=0;function nq(e,t,i,n){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),n&&(n[e]=0))}function nH({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(e={},i=t?.()){this.id=nB++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(nF.nodes=nF.calculatedTargetDeltas=nF.calculatedProjections=0),this.nodes.forEach(nK),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nG),h.addProjectionMetrics&&h.addProjectionMetrics(nF)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nm)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new j),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=nh(t)&&!(nh(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),e){let i,n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=S.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(f(n),e(s-t))};return m.setup(n,!0),()=>f(n)}(n,250),ns.hasAnimatedSinceResize&&(ns.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n9,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),u=!this.targetLayout||!nV(this.targetLayout,n),c=!t&&i;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(s,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n3),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let n=i.props[O];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(e||i))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nJ);return}this.isUpdating||this.nodes.forEach(nZ),this.isUpdating=!1,this.nodes.forEach(nQ),this.nodes.forEach(nW),this.nodes.forEach(nX),this.clearAllSnapshots();let e=S.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ne.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nY),this.sharedNodes.forEach(n5)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iw(this.snapshot.measuredBox.x)||iw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ik(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!n_(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||iO(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),rt((t=n).x),rt(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return ik();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:e}=this.root;e&&(iz(t.x,e.offset.x),iz(t.y,e.offset.y))}return t}removeElementScroll(e){let t=ik();if(nS(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nS(t,e),iz(t.x,r.offset.x),iz(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let i=ik();nS(i,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iU(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iO(n.latestValues)&&iU(i,n.latestValues)}return iO(this.latestValues)&&iU(i,this.latestValues),i}removeTransform(e){let t=ik();nS(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iO(i.latestValues))continue;i_(i.latestValues)&&i.updateSnapshot();let n=ik();nS(n,i.measurePageBox()),nM(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iO(this.latestValues)&&nM(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ik(),this.relativeTargetOrigin=ik(),iT(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ik(),this.targetWithTransforms=ik()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iE(s.x,a.x,o.x),iE(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nS(this.target,this.layout.layoutBox),iL(this.target,this.targetDelta)):nS(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ik(),this.relativeTargetOrigin=ik(),iT(this.relativeTargetOrigin,this.target,e.target),nS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&nF.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||i_(this.parent.latestValues)||iD(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nS(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(e,t,i,n=!1){let r,s,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){s=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iU(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,iL(e,s)),n&&iO(r.latestValues)&&iU(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=ik());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nT(this.prevProjectionDelta.x,this.projectionDelta.x),nT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ij(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&nL(this.projectionDelta.x,this.prevProjectionDelta.x)&&nL(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),h.value&&nF.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iC(),this.projectionDelta=iC(),this.projectionDeltaWithTransform=iC()}setAnimationOrigin(e,t=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},a=iC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=ik(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(n8));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(n6(a.x,e.x,n),n6(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;iT(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,n4(p.x,m.x,f.x,g),n4(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,h=i,nO(u.x,h.x)&&nO(u.y,h.y))&&(this.isProjectionDirty=!1),i||(i=ik()),nS(i,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,n,r,s){r?(e.opacity=eT(0,i.opacity??1,nw(n)),e.opacityExit=eT(t.opacity??1,0,nP(n))):s&&(e.opacity=eT(t.opacity??1,i.opacity??1,n));for(let r=0;r<ny;r++){let s=`border${ng[r]}Radius`,a=nb(t,s),o=nb(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||nx(a)===nx(o)?(e[s]=Math.max(eT(nv(a),nv(o),n),0),(el.test(o)||el.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=eT(t.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{ns.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=R(0)),this.currentAnimation=function(e,t,i){let n=M(e)?e:R(e);return n.start(t6("",n,t,i)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:n,latestValues:r}=e;if(t&&i&&n){if(this!==e&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ik();let t=iw(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let n=iw(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+n}nS(t,i),iU(t,r),ij(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nz),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let n={};i.z&&nq("z",e,n,this.animationValues);for(let t=0;t<nU.length;t++)nq(`rotate${nU[t]}`,e,n,this.animationValues),nq(`skew${nU[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return n$;let t={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=nf(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none",t;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nf(e?.pointerEvents)||""),this.hasProjected&&!iO(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,i){let n="",r=e.x.translate/t.x,s=e.y.translate/t.y,a=i?.z||0;if((r||s||a)&&(n=`translate3d(${r}px, ${s}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:s,skewX:a,skewY:o}=i;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(t.transform=i(r,t.transform));let{x:s,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,nl){if(void 0===r[e])continue;let{correct:i,applyTo:s,isCSSVariable:a}=nl[e],o="none"===t.transform?r[e]:i(r[e],n);if(s){let e=s.length;for(let i=0;i<e;i++)t[s[i]]=o}else a?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=n===this?nf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(nJ),this.root.sharedNodes.clear()}}}function nW(e){e.updateLayout()}function nX(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=e.layout,{animationType:r}=e.options,s=t.source!==e.layout.source;"size"===r?iM(e=>{let n=s?t.measuredBox[e]:t.layoutBox[e],r=iw(n);n.min=i[e].min,n.max=n.min+r}):ri(r,t.layoutBox,i)&&iM(n=>{let r=s?t.measuredBox[n]:t.layoutBox[n],a=iw(i[n]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=iC();ij(a,i,t.layoutBox);let o=iC();s?ij(o,e.applyTransform(n,!0),t.measuredBox):ij(o,i,t.layoutBox);let l=!n_(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let a=ik();iT(a,t.layoutBox,r.layoutBox);let o=ik();iT(o,i,s.layoutBox),nV(a,o)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nK(e){h.value&&nF.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nG(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nY(e){e.clearSnapshot()}function nJ(e){e.clearMeasurements()}function nZ(e){e.isLayoutDirty=!1}function nQ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n0(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function n1(e){e.resolveTargetDelta()}function n2(e){e.calcProjection()}function n3(e){e.resetSkewAndRotation()}function n5(e){e.removeLeadSnapshot()}function n6(e,t,i){e.translate=eT(t.translate,0,i),e.scale=eT(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function n4(e,t,i,n){e.min=eT(t.min,i.min,n),e.max=eT(t.max,i.max,n)}function n8(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let n9={duration:.45,ease:[.4,0,.1,1]},n7=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),re=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function rt(e){e.min=re(e.min),e.max=re(e.max)}function ri(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nI(t)-nI(i)))}function rn(e){return e!==e.root&&e.scroll?.wasRoot}let rr=nH({attachResizeListener:(e,t)=>im(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ra=nH({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rs.current){let e=new rr({});e.mount(window),e.setOptions({layoutScroll:!0}),rs.current=e}return rs.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ro(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,i=(void 0)??t.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}(e),n=new AbortController;return[i,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function rl(e){return!("touch"===e.pointerType||ip.x||ip.y)}function ru(e,t,i){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&m.postRender(()=>r(t,iy(t)))}class rc extends iu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[n,r,s]=ro(e,i),a=e=>{if(!rl(e))return;let{target:i}=e,n=t(i,e);if("function"!=typeof n||!i)return;let s=e=>{rl(e)&&(n(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(e=>{e.addEventListener("pointerenter",a,r)}),s}(e,(e,t)=>(ru(this.node,t,"Start"),e=>ru(this.node,e,"End"))))}unmount(){}}class rd extends iu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=V(im(this.node.current,"focus",()=>this.onFocus()),im(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rh=(e,t)=>!!t&&(e===t||rh(e,t.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rm=new WeakSet;function rf(e){return t=>{"Enter"===t.key&&e(t)}}function rg(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let ry=(e,t)=>{let i=e.currentTarget;if(!i)return;let n=rf(()=>{if(rm.has(i))return;rg(i,"down");let e=rf(()=>{rg(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>rg(i,"cancel"),t)});i.addEventListener("keydown",n,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),t)};function rv(e){return ig(e)&&!(ip.x||ip.y)}function rx(e,t,i){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(t,iy(t)))}class rb extends iu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[n,r,s]=ro(e,i),a=e=>{let n=e.currentTarget;if(!rv(e))return;rm.add(n);let s=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rm.has(n)&&rm.delete(n),rv(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,n===window||n===document||i.useGlobalTarget||rh(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return n.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,r),tG(e))&&(e.addEventListener("focus",e=>ry(e,r)),rp.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(rx(this.node,t,"Start"),(e,{success:t})=>rx(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rw=new WeakMap,rP=new WeakMap,rj=e=>{let t=rw.get(e.target);t&&t(e)},rE=e=>{e.forEach(rj)},rS={some:0,all:1};class rT extends iu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:n="some",once:r}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rS[n]};return function(e,t,i){let n=function({root:e,...t}){let i=e||document;rP.has(i)||rP.set(i,{});let n=rP.get(i),r=JSON.stringify(t);return n[r]||(n[r]=new IntersectionObserver(rE,{root:e,...t})),n[r]}(t);return rw.set(e,i),n.observe(e),()=>{rw.delete(e),n.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=t?i:n;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let rA=(0,nt.createContext)({strict:!1}),rC=(0,nt.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),rR=(0,nt.createContext)({});function rk(e){return r(e.animate)||ii.some(t=>ie(e[t]))}function rM(e){return!!(rk(e)||e.variants)}function rN(e){return Array.isArray(e)?e.join(" "):e}let r_="undefined"!=typeof window,rO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rD={};for(let e in rO)rD[e]={isEnabled:t=>rO[e].some(e=>!!t[e])};let rV=Symbol.for("motionComponentSymbol"),rI=r_?nt.useLayoutEffect:nt.useEffect;function rL(e,{layout:t,layoutId:i}){return x.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!nl[e]||"opacity"===e)}let rz=(e,t)=>t&&"number"==typeof e?t.transform(e):e,rF={...K,transform:Math.round},rU={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:eo,rotateX:eo,rotateY:eo,rotateZ:eo,scale:Y,scaleX:Y,scaleY:Y,scaleZ:Y,skew:eo,skewX:eo,skewY:eo,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:G,originX:eh,originY:eh,originZ:eu,zIndex:rF,fillOpacity:G,strokeOpacity:G,numOctaves:rF},r$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rB=v.length;function rq(e,t,i){let{style:n,vars:r,transformOrigin:s}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(x.has(e)){a=!0;continue}if(q(e)){r[e]=i;continue}{let t=rz(i,rU[e]);e.startsWith("origin")?(o=!0,s[e]=t):n[e]=t}}if(!t.transform&&(a||i?n.transform=function(e,t,i){let n="",r=!0;for(let s=0;s<rB;s++){let a=v[s],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=rz(o,rU[a]);if(!l){r=!1;let t=r$[a]||a;n+=`${t}(${e}) `}i&&(t[a]=e)}}return n=n.trim(),i?n=i(t,r?"":n):r&&(n="none"),n}(t,e.transform,i):n.transform&&(n.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=s;n.transformOrigin=`${e} ${t} ${i}`}}let rH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rW(e,t,i){for(let n in t)M(t[n])||rL(n,i)||(e[n]=t[n])}let rX={offset:"stroke-dashoffset",array:"stroke-dasharray"},rK={offset:"strokeDashoffset",array:"strokeDasharray"};function rG(e,{attrX:t,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:a=0,...o},l,u,c){if(rq(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==r&&function(e,t,i=1,n=0,r=!0){e.pathLength=1;let s=r?rX:rK;e[s.offset]=eu.transform(-n);let a=eu.transform(t),o=eu.transform(i);e[s.array]=`${a} ${o}`}(d,r,s,a,!1)}let rY=()=>({...rH(),attrs:{}}),rJ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),rZ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rQ(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||rZ.has(e)}let r0=e=>!rQ(e);try{!function(e){e&&(r0=t=>t.startsWith("on")?!rQ(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let r1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r2(e){if("string"!=typeof e||e.includes("-"));else if(r1.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let r3=e=>(t,i)=>{let n=(0,nt.useContext)(rR),s=(0,nt.useContext)(ni),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,n,s){return{latestValues:function(e,t,i,n){let s={},o=n(e,{});for(let e in o)s[e]=nf(o[e]);let{initial:l,animate:u}=e,c=rk(e),d=rM(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!i&&!1===i.initial,p=(h=h||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let t=Array.isArray(p)?p:[p];for(let i=0;i<t.length;i++){let n=a(e,t[i]);if(n){let{transitionEnd:e,transition:t,...i}=n;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(s[e]=t)}for(let t in e)s[t]=e[t]}}}return s}(i,n,s,e),renderState:t()}})(e,t,n,s);return i?o():function(e){let t=(0,nt.useRef)(null);return null===t.current&&(t.current=e()),t.current}(o)};function r5(e,t,i){let{style:n}=e,r={};for(let s in n)(M(n[s])||t.style&&M(t.style[s])||rL(s,e)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r6={useVisualState:r3({scrapeMotionValuesFromProps:r5,createRenderState:rH})};function r4(e,t,i){let n=r5(e,t,i);for(let i in e)(M(e[i])||M(t[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return n}let r8={useVisualState:r3({scrapeMotionValuesFromProps:r4,createRenderState:rY})},r9=e=>t=>t.test(e),r7=[K,eu,el,eo,ed,ec,{test:e=>"auto"===e,parse:e=>e}],se=e=>r7.find(r9(e)),st=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),si=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,sn=e=>/^0[^.\s]+$/u.test(e),sr=new Set(["brightness","contrast","saturate","opacity"]);function ss(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=i.match(Z)||[];if(!n)return e;let r=i.replace(n,""),s=+!!sr.has(t);return n!==i&&(s*=100),t+"("+s+r+")"}let sa=/\b([a-z-]*)\(.*?\)/gu,so={...ej,getAnimatableNone:e=>{let t=e.match(sa);return t?t.map(ss).join(" "):e}},sl={...rU,color:em,backgroundColor:em,outlineColor:em,fill:em,stroke:em,borderColor:em,borderTopColor:em,borderRightColor:em,borderBottomColor:em,borderLeftColor:em,filter:so,WebkitFilter:so},su=e=>sl[e];function sc(e,t){let i=su(e);return i!==so&&(i=ej),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let sd=new Set(["auto","none","0"]);class sh extends tD{constructor(e,t,i,n,r){super(e,t,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let n=e[i];if("string"==typeof n&&W(n=n.trim())){let r=function e(t,i,n=1){$(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,s]=function(e){let t=si.exec(e);if(!t)return[,];let[,i,n,r]=t;return[`--${i??n}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return st(e)?parseFloat(e):e}return W(s)?e(s,i,n+1):s}(n,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!b.has(i)||2!==e.length)return;let[n,r]=e,s=se(n),a=se(r);if(s!==a)if(tS(s)&&tS(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else tC[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||sn(n)))&&i.push(t)}i.length&&function(e,t,i){let n,r=0;for(;r<e.length&&!n;){let t=e[r];"string"==typeof t&&!sd.has(t)&&ex(t).values.length&&(n=e[r]),r++}if(n&&i)for(let r of t)e[r]=sc(i,n)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tC[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(i,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=tC[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let sp=[...r7,em,ej],sm=e=>sp.find(r9(e)),sf={current:null},sg={current:!1},sy=new WeakMap,sv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sx{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tD,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=S.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,m.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rk(t),this.isVariantNode=rM(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&M(t)&&t.set(o[e],!1)}}mount(e){this.current=e,sy.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sg.current||function(){if(sg.current=!0,r_)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>sf.current=e.matches;e.addListener(t),t()}else sf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=x.has(e);n&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),s(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rD){let t=rD[e];if(!t)continue;let{isEnabled:i,Feature:n}=t;if(!this.features[e]&&n&&i(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ik()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sv.length;t++){let i=sv[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=e["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(e,t,i){for(let n in t){let r=t[n],s=i[n];if(M(r))e.addValue(n,r);else if(M(s))e.addValue(n,R(r,{owner:e}));else if(s!==r)if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(n);e.addValue(n,R(void 0!==t?t:r,{owner:e}))}}for(let n in i)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=R(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(st(i)||sn(i))?i=parseFloat(i):!sm(i)&&ej.test(t)&&(i=sc(e,t)),this.setBaseTarget(e,M(i)?i.get():i)),M(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=a(this.props,i,this.presenceContext?.custom);n&&(t=n[e])}if(i&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||M(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new j),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sb extends sx{constructor(){super(...arguments),this.KeyframeResolver=sh}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;M(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function sw(e,{style:t,vars:i},n,r){for(let s in Object.assign(e.style,t,r&&r.getProjectionStyles(n)),i)e.style.setProperty(s,i[s])}class sP extends sb{constructor(){super(...arguments),this.type="html",this.renderInstance=sw}readValueFromInstance(e,t){if(x.has(t))return this.projection?.isProjecting?tw(t):tj(e,t);{let i=window.getComputedStyle(e),n=(q(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return i$(e,t)}build(e,t,i){rq(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return r5(e,t,i)}}let sj=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ik}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(x.has(t)){let e=su(t);return e&&e.default||0}return t=sj.has(t)?t:_(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return r4(e,t,i)}build(e,t,i){rG(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,n){for(let i in sw(e,t,void 0,n),t.attrs)e.setAttribute(sj.has(i)?i:_(i),t.attrs[i])}mount(e){this.isSVGTag=rJ(e.tagName),super.mount(e)}}let sS=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((i8={animation:{Feature:ic},exit:{Feature:ih},inView:{Feature:rT},tap:{Feature:rb},focus:{Feature:rd},hover:{Feature:rc},pan:{Feature:i4},drag:{Feature:i5,ProjectionNode:ra,MeasureLayout:nc},layout:{ProjectionNode:ra,MeasureLayout:nc}},i9=(e,t)=>r2(e)?new sE(t):new sP(t,{allowProjection:e!==nt.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:n,Component:r}){function s(e,s){var a,o,l;let u,c={...(0,nt.useContext)(rC),...e,layoutId:function({layoutId:e}){let t=(0,nt.useContext)(nn).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:i}=function(e,t){if(rk(e)){let{initial:t,animate:i}=e;return{initial:!1===t||ie(t)?t:void 0,animate:ie(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,nt.useContext)(rR));return(0,nt.useMemo)(()=>({initial:t,animate:i}),[rN(t),rN(i)])}(e),p=n(e,d);if(!d&&r_){o=0,l=0,(0,nt.useContext)(rA).strict;let e=function(e){let{drag:t,layout:i}=rD;if(!t&&!i)return{};let n={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,i,n,r){let{visualElement:s}=(0,nt.useContext)(rR),a=(0,nt.useContext)(rA),o=(0,nt.useContext)(ni),l=(0,nt.useContext)(rC).reducedMotion,u=(0,nt.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,d=(0,nt.useContext)(nr);c&&!c.projection&&r&&("html"===c.type||"svg"===c.type)&&function(e,t,i,n){let{layoutId:r,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!a||o&&iq(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,i,r,d);let h=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{c&&h.current&&c.update(i,o)});let p=i[O],m=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return rI(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),ne.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,nt.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),c}(r,p,c,t,e.ProjectionNode)}return(0,i7.jsxs)(rR.Provider,{value:h,children:[u&&h.visualElement?(0,i7.jsx)(u,{visualElement:h.visualElement,...c}):null,i(r,e,(a=h.visualElement,(0,nt.useCallback)(e=>{e&&p.onMount&&p.onMount(e),a&&(e?a.mount(e):a.unmount()),s&&("function"==typeof s?s(e):iq(s)&&(s.current=e))},[a])),p,d,h.visualElement)]})}e&&function(e){for(let t in e)rD[t]={...rD[t],...e[t]}}(e),s.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let a=(0,nt.forwardRef)(s);return a[rV]=r,a}({...r2(e)?r8:r6,preloadedFeatures:i8,useRender:function(e=!1){return(t,i,n,{latestValues:r},s)=>{let a=(r2(t)?function(e,t,i,n){let r=(0,nt.useMemo)(()=>{let i=rY();return rG(i,t,rJ(n),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};rW(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},n=function(e,t){let i=e.style||{},n={};return rW(n,i,e),Object.assign(n,function({transformTemplate:e},t){return(0,nt.useMemo)(()=>{let i=rH();return rq(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,t),o=function(e,t,i){let n={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(r0(r)||!0===i&&rQ(r)||!t&&!rQ(r)||e.draggable&&r.startsWith("onDrag"))&&(n[r]=e[r]);return n}(i,"string"==typeof t,e),l=t!==nt.Fragment?{...o,...a,ref:n}:{},{children:u}=i,c=(0,nt.useMemo)(()=>M(u)?u.get():u,[u]);return(0,nt.createElement)(t,{...l,children:c})}}(t),createVisualElement:i9,Component:e})}))},1135:()=>{},1195:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\layout\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Navigation.tsx","default")},1204:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>m});var n=i(7413),r=i(1195),s=i(4712),a=i(2234),o=i(2119),l=i(4230),u=i(610),c=i(200),d=i(7576),h=i(3041),p=i(2246);function m(){return(0,n.jsxs)("div",{className:"min-h-screen",children:[(0,n.jsx)(r.default,{}),(0,n.jsxs)("main",{children:[(0,n.jsx)(a.default,{}),(0,n.jsx)(o.default,{}),(0,n.jsx)(l.default,{}),(0,n.jsx)(u.default,{}),(0,n.jsx)(c.default,{}),(0,n.jsx)(d.default,{}),(0,n.jsx)(h.default,{}),(0,n.jsx)(p.default,{})]}),(0,n.jsx)(s.default,{})]})}},1261:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return o}});let n=i(4985),r=i(4953),s=i(6533),a=n._(i(1933));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/rizqibennington.github.io/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},1437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return r},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let n=i(4722),r=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>r.find(t=>e.startsWith(t)))}function a(e){let t,i,s;for(let n of e.split("/"))if(i=r.find(e=>n.startsWith(e))){[t,s]=e.split(i,2);break}if(!t||!i||!s)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),i){case"(.)":s="/"===t?"/"+s:t+"/"+s;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:s}}},1480:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:n,blurHeight:r,blurDataURL:s,objectFit:a}=e,o=n?40*n:t,l=r?40*r:i,u=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},1550:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1658:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let n=i(8304),r=function(e){return e&&e.__esModule?e:{default:e}}(i(8671)),s=i(6341),a=i(4396),o=i(660),l=i(4722),u=i(2958),c=i(5499);function d(e){let t=r.default.dirname(e);if(e.endsWith("/sitemap"))return"";let i="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(i=(0,o.djb2Hash)(t).toString(36).slice(0,6)),i}function h(e,t,i){let n=(0,l.normalizeAppPath)(e),o=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,s.interpolateDynamicPath)(n,t,o),{name:h,ext:p}=r.default.parse(i),m=d(r.default.posix.join(e,h)),f=m?`-${m}`:"";return(0,u.normalizePathSep)(r.default.join(c,`${h}${f}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,i="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":i=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:s}=r.default.parse(t);t=r.default.posix.join(e,`${n}${i?`-${i}`:""}${s}`,"route")}return t}function m(e,t){let i=e.endsWith("/route"),n=i?e.slice(0,-6):e,r=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${r}`)+(i?"/route":"")}},1869:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},1933:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:n,width:r,quality:s}=e,a=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(n)+"&w="+r+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),i.__next_img_default=!0;let n=i},1967:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var n=i(687),r=i(1121),s=i(7472),a=i(3210);let o=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),[i,o]=(0,a.useState)({certifications:0,experience:0,projects:0,industries:0}),l=[{key:"certifications",target:3,label:"Certification"},{key:"experience",target:2,label:"Years Working Experience"},{key:"projects",target:50,label:"Projects Completed"},{key:"industries",target:4,label:"Industry Fields"}];(0,a.useEffect)(()=>{t&&l.forEach(e=>{let t=0,i=e.target,n=i/125,r=setInterval(()=>{(t+=n)>=i?(o(t=>({...t,[e.key]:i})),clearInterval(r)):o(i=>({...i,[e.key]:Math.floor(t)}))},16)})},[t,l]);let u={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,n.jsxs)("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden",ref:e,children:[(0,n.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,n.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}})}),(0,n.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,n.jsx)(r.P.div,{className:"grid grid-cols-2 md:grid-cols-4 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:l.map((e,s)=>(0,n.jsx)(r.P.div,{className:"text-center text-white",variants:u,children:(0,n.jsxs)(r.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300",whileHover:{scale:1.05,boxShadow:"0 10px 30px rgba(255,255,255,0.1)"},transition:{duration:.3},children:[(0,n.jsx)(r.P.div,{className:"text-4xl md:text-5xl font-bold mb-2",initial:{scale:0},animate:t?{scale:1}:{scale:0},transition:{duration:.5,delay:.2*s,type:"spring",stiffness:100},children:i[e.key]}),(0,n.jsx)(r.P.div,{className:"text-blue-100 font-medium text-sm md:text-base",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:.2*s+.3},children:e.label})]})},e.key))}),(0,n.jsx)(r.P.div,{className:"absolute top-10 left-10 w-20 h-20 bg-white/5 rounded-full blur-xl",animate:{scale:[1,1.2,1],opacity:[.3,.6,.3]},transition:{duration:4,repeat:1/0,ease:"easeInOut"}}),(0,n.jsx)(r.P.div,{className:"absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-xl",animate:{scale:[1.2,1,1.2],opacity:[.4,.2,.4]},transition:{duration:5,repeat:1/0,ease:"easeInOut"}})]})]})}},2119:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\AboutSection.tsx","default")},2234:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\HeroSection.tsx","default")},2246:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\ContactSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ContactSection.tsx","default")},2437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return r}});let n=i(5362);function r(e,t){let i=[],r=(0,n.pathToRegexp)(e,i,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(r.source),r.flags):r,i);return(e,n)=>{if("string"!=typeof e)return!1;let r=s(e);if(!r)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of i)"number"==typeof e.name&&delete r.params[e.name];return{...n,...r.params}}}},2688:(e,t,i)=>{"use strict";i.d(t,{A:()=>d});var n=i(3210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),a=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:s="",children:a,iconNode:c,...d},h)=>(0,n.createElement)("svg",{ref:h,...u,width:t,height:t,stroke:e,strokeWidth:r?24*Number(i)/Number(t):i,className:o("lucide",s),...!a&&!l(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let i=(0,n.forwardRef)(({className:i,...s},l)=>(0,n.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${r(a(e))}`,`lucide-${e}`,i),...s}));return i.displayName=a(e),i}},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return n}});let i=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2785:(e,t)=>{"use strict";function i(e){let t={};for(let[i,n]of e.entries()){let e=t[i];void 0===e?t[i]=n:Array.isArray(e)?e.push(n):t[i]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function r(e){let t=new URLSearchParams;for(let[i,r]of Object.entries(e))if(Array.isArray(r))for(let e of r)t.append(i,n(e));else t.set(i,n(r));return t}function s(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,n]of t.entries())e.append(i,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},2958:(e,t)=>{"use strict";function i(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(3210);function r(e,t){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=i.current;e&&(i.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(i.current=s(e,n)),t&&(r.current=s(t,n))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3041:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\StatsSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\StatsSection.tsx","default")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return r}});let i=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function r(e){return i.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return r}}),i(4827);let n=i(2785);function r(e,t,i){void 0===i&&(i=!0);let r=new URL("http://n"),s=t?new URL(t,r):e.startsWith(".")?new URL("http://n"):r,{pathname:a,searchParams:o,search:l,hash:u,href:c,origin:d}=new URL(e,s);if(d!==r.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:c.slice(d.length)}}},3872:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(2688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},3873:e=>{"use strict";e.exports=require("path")},4230:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\ExperienceSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ExperienceSection.tsx","default")},4396:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=i(6143),r=i(1437),s=i(3293),a=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let i=e.startsWith("...");return i&&(e=e.slice(3)),{key:e,repeat:i,optional:t}}function c(e,t,i){let n={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=r.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(o);if(e&&a&&a[2]){let{key:t,optional:i,repeat:r}=u(a[2]);n[t]={pos:l++,repeat:r,optional:i},c.push("/"+(0,s.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:r}=u(a[2]);n[e]={pos:l++,repeat:t,optional:r},i&&a[1]&&c.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=t?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,s.escapeStringRegexp)(d));t&&a&&a[3]&&c.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:i=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:r=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=c(e,i,n),o=s;return r||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function h(e){let t,{interceptionMarker:i,getSafeRouteKey:n,segment:r,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(r),p=c.replace(/\W/g,"");o&&(p=""+o+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=n());let f=p in a;o?a[p]=""+o+c:a[p]=c;let g=i?(0,s.escapeStringRegexp)(i):"";return t=f&&l?"\\k<"+p+">":h?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,i,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},m=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=r.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(o);if(e&&a&&a[2])m.push(h({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,s.escapeStringRegexp)(a[1]));let e=h({getSafeRouteKey:d,segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,s.escapeStringRegexp)(c));i&&a&&a[3]&&m.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(e,t){var i,n,r;let s=p(e,t.prefixRouteKeys,null!=(i=t.includeSuffix)&&i,null!=(n=t.includePrefix)&&n,null!=(r=t.backreferenceDuplicateKeys)&&r),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function f(e,t){let{parameterizedRoute:i}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===i)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:r}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+r+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o,metadata:()=>a});var n=i(7413),r=i(7255),s=i.n(r);i(1135);let a={title:"Rizqi Rahmansyah - Web Portfolio",description:"Professional web developer portfolio showcasing skills in HTML, CSS, JavaScript, React, PHP, and more.",keywords:"web developer, portfolio, React, JavaScript, PHP, HTML, CSS",authors:[{name:"Rizqi Rahmansyah"}],viewport:"width=device-width, initial-scale=1, shrink-to-fit=no"};function o({children:e}){return(0,n.jsxs)("html",{lang:"en",children:[(0,n.jsx)("head",{children:(0,n.jsx)("link",{rel:"icon",href:"/images/aboutme.png",type:"image/x-icon"})}),(0,n.jsx)("body",{className:`${s().variable} font-poppins antialiased`,children:e})]})}},4600:(e,t,i)=>{Promise.resolve().then(i.bind(i,587)),Promise.resolve().then(i.bind(i,5836)),Promise.resolve().then(i.bind(i,1018)),Promise.resolve().then(i.bind(i,843)),Promise.resolve().then(i.bind(i,220)),Promise.resolve().then(i.bind(i,6364)),Promise.resolve().then(i.bind(i,803)),Promise.resolve().then(i.bind(i,6256)),Promise.resolve().then(i.bind(i,638)),Promise.resolve().then(i.bind(i,1967))},4604:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||i&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},4605:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},4712:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Footer.tsx","default")},4722:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let n=i(5531),r=i(5499);function s(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,i,n)=>!t||(0,r.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,t=e(...r)),t}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>r.test(e);function a(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class f extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},4953:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(148);let n=i(1480),r=i(2756),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,c,d,{src:h,sizes:p,unoptimized:m=!1,priority:f=!1,loading:g,className:y,quality:v,width:x,height:b,fill:w=!1,style:P,overrideSrc:j,onLoad:E,onLoadingComplete:S,placeholder:T="empty",blurDataURL:A,fetchPriority:C,decoding:R="async",layout:k,objectFit:M,objectPosition:N,lazyBoundary:_,lazyRoot:O,...D}=e,{imgConf:V,showAltText:I,blurComplete:L,defaultLoader:z}=t,F=V||r.imageConfigDefault;if("allSizes"in F)u=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(i=F.qualities)?void 0:i.sort((e,t)=>e-t);u={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===z)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let U=D.loader||z;delete D.loader,delete D.srcSet;let $="__next_img_default"in U;if($){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=U;U=t=>{let{config:i,...n}=t;return e(n)}}if(k){"fill"===k&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(P={...P,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!p&&(p=t)}let B="",q=o(x),H=o(b);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,d=e.blurHeight,A=A||e.blurDataURL,B=e.src,!w)if(q||H){if(q&&!H){let t=q/e.width;H=Math.round(e.height*t)}else if(!q&&H){let t=H/e.height;q=Math.round(e.width*t)}}else q=e.width,H=e.height}let W=!f&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:B)||h.startsWith("data:")||h.startsWith("blob:"))&&(m=!0,W=!1),u.unoptimized&&(m=!0),$&&!u.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(m=!0);let X=o(v),K=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:N}:{},I?{}:{color:"transparent"},P),G=L||"empty"===T?null:"blur"===T?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:q,heightInt:H,blurWidth:c,blurHeight:d,blurDataURL:A||"",objectFit:K.objectFit})+'")':'url("'+T+'")',Y=s.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,J=G?{backgroundSize:Y,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:G}:{},Z=function(e){let{config:t,src:i,unoptimized:n,width:r,quality:s,sizes:a,loader:o}=e;if(n)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:n,allSizes:r}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(i);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,a),c=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>o({config:t,src:i,quality:s,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:o({config:t,src:i,quality:s,width:l[c]})}}({config:u,src:h,unoptimized:m,width:q,quality:X,sizes:p,loader:U});return{props:{...D,loading:W?"lazy":g,fetchPriority:C,width:q,height:H,decoding:R,className:y,style:{...K,...J},sizes:Z.sizes,srcSet:Z.srcSet,src:j||Z.src},meta:{unoptimized:m,priority:f,placeholder:T,fill:w}}}},4959:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.AmpContext},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var i=function(e){for(var t=[],i=0;i<e.length;){var n=e[i];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:i,value:e[i++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});continue}if("{"===n){t.push({type:"OPEN",index:i,value:e[i++]});continue}if("}"===n){t.push({type:"CLOSE",index:i,value:e[i++]});continue}if(":"===n){for(var r="",s=i+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){r+=e[s++];continue}break}if(!r)throw TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:r}),i=s;continue}if("("===n){var o=1,l="",s=i+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:l}),i=s;continue}t.push({type:"CHAR",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),n=t.prefixes,s=void 0===n?"./":n,a="[^"+r(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<i.length&&i[u].type===e)return i[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=i[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<i.length;){var m=d("CHAR"),f=d("NAME"),g=d("PATTERN");if(f||g){var y=m||"";-1===s.indexOf(y)&&(c+=y,y=""),c&&(o.push(c),c=""),o.push({name:f||l++,prefix:y,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var v=m||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(o.push(c),c=""),d("OPEN")){var y=p(),x=d("NAME")||"",b=d("PATTERN")||"",w=p();h("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?a:b,prefix:y,suffix:w,modifier:d("MODIFIER")||""});continue}h("END")}return o}function i(e,t){void 0===t&&(t={});var i=s(t),n=t.encode,r=void 0===n?function(e){return e}:n,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",i)});return function(t){for(var i="",n=0;n<e.length;n++){var s=e[n];if("string"==typeof s){i+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,c="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var d=0;d<a.length;d++){var h=r(a[d],s);if(o&&!l[n].test(h))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');i+=s.prefix+h+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=r(String(a),s);if(o&&!l[n].test(h))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');i+=s.prefix+h+s.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return i}}function n(e,t,i){void 0===i&&(i={});var n=i.decode,r=void 0===n?function(e){return e}:n;return function(i){var n=e.exec(i);if(!n)return!1;for(var s=n[0],a=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var i=t[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=n[e].split(i.prefix+i.suffix).map(function(e){return r(e,i)}):o[i.name]=r(n[e],i)}}(l);return{path:s,index:a,params:o}}}function r(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,i){void 0===i&&(i={});for(var n=i.strict,a=void 0!==n&&n,o=i.start,l=i.end,u=i.encode,c=void 0===u?function(e){return e}:u,d="["+r(i.endsWith||"")+"]|$",h="["+r(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",m=0;m<e.length;m++){var f=e[m];if("string"==typeof f)p+=r(c(f));else{var g=r(c(f.prefix)),y=r(c(f.suffix));if(f.pattern)if(t&&t.push(f),g||y)if("+"===f.modifier||"*"===f.modifier){var v="*"===f.modifier?"?":"";p+="(?:"+g+"((?:"+f.pattern+")(?:"+y+g+"(?:"+f.pattern+"))*)"+y+")"+v}else p+="(?:"+g+"("+f.pattern+")"+y+")"+f.modifier;else p+="("+f.pattern+")"+f.modifier;else p+="(?:"+g+y+")"+f.modifier}}if(void 0===l||l)a||(p+=h+"?"),p+=i.endsWith?"(?="+d+")":"$";else{var x=e[e.length-1],b="string"==typeof x?h.indexOf(x[x.length-1])>-1:void 0===x;a||(p+="(?:"+h+"(?="+d+"))?"),b||(p+="(?="+h+"|"+d+")")}return new RegExp(p,s(i))}function o(t,i,n){if(t instanceof RegExp){if(!i)return t;var r=t.source.match(/\((?!\?)/g);if(r)for(var l=0;l<r.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,i,n).source}).join("|")+")",s(n)):a(e(t,n),i,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return i(e(t,n),n)},t.tokensToFunction=i,t.match=function(e,t){var i=[];return n(o(e,i,t),i,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return h}});let n=i(5362),r=i(3293),s=i(6759),a=i(1437),o=i(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,i,n){void 0===i&&(i=[]),void 0===n&&(n=[]);let r={},s=i=>{let n,s=i.key;switch(i.type){case"header":s=s.toLowerCase(),n=e.headers[s];break;case"cookie":n="cookies"in e?e.cookies[i.key]:(0,o.getCookieParser)(e.headers)()[i.key];break;case"query":n=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!i.value&&n)return r[function(e){let t="";for(let i=0;i<e.length;i++){let n=e.charCodeAt(i);(n>64&&n<91||n>96&&n<123)&&(t+=e[i])}return t}(s)]=n,!0;if(n){let e=RegExp("^"+i.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{r[e]=t.groups[e]}):"host"===i.type&&t[0]&&(r.host=t[0])),!0}return!1};return!(!i.every(e=>s(e))||n.some(e=>s(e)))&&r}function c(e,t){if(!e.includes(":"))return e;for(let i of Object.keys(t))e.includes(":"+i)&&(e=e.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let i of Object.keys({...e.params,...e.query}))i&&(t=t.replace(RegExp(":"+(0,r.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,s.parseUrl)(t),n=i.pathname;n&&(n=l(n));let a=i.href;a&&(a=l(a));let o=i.hostname;o&&(o=l(o));let u=i.hash;return u&&(u=l(u)),{...i,pathname:n,hostname:o,href:a,hash:u}}function h(e){let t,i,r=Object.assign({},e.query),s=d(e),{hostname:o,query:u}=s,h=s.pathname;s.hash&&(h=""+h+s.hash);let p=[],m=[];for(let e of((0,n.pathToRegexp)(h,m),m))p.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))p.push(t.name)}let f=(0,n.compile)(h,{validate:!1});for(let[i,r]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(r)?u[i]=r.map(t=>c(l(t),e.params)):"string"==typeof r&&(u[i]=c(l(r),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(i){"(..)(..)"===i?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=i;break}}try{let[n,r]=(i=f(e.params)).split("#",2);t&&(s.hostname=t(e.params)),s.pathname=n,s.hash=(r?"#":"")+(r||""),delete s.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return s.query={...r,...s.query},{newUrl:i,destQuery:u,parsedDestination:s}}},5531:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},5562:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var n=i(5239),r=i(8088),s=i(8170),a=i.n(s),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7941))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7941))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},5836:(e,t,i)=>{"use strict";i.d(t,{default:()=>u});var n=i(687),r=i(3210),s=i(1121),a=i(2688);let o=(0,a.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),l=(0,a.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),u=()=>{let[e,t]=(0,r.useState)(!1),[i,a]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{a(window.scrollY>150)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let u=[{href:"#home-section",label:"Home"},{href:"#about-section",label:"About"},{href:"#resume-section",label:"Experience"},{href:"#services-section",label:"Services"},{href:"#skills-section",label:"Skills"},{href:"#projects-section",label:"Projects"},{href:"#contact-section",label:"Contact"}],c=e=>{let i=document.querySelector(e);i&&(i.scrollIntoView({behavior:"smooth",block:"start"}),t(!1))};return(0,n.jsx)(s.P.nav,{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${i?"bg-gray-900/95 backdrop-blur-sm shadow-lg":"bg-transparent"}`,initial:{y:-100},animate:{y:0},transition:{duration:.5},children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,n.jsx)(s.P.a,{href:"#home-section",className:"text-2xl font-bold text-white hover:text-blue-400 transition-colors",onClick:e=>{e.preventDefault(),c("#home-section")},whileHover:{scale:1.05},whileTap:{scale:.95},children:"RR"}),(0,n.jsx)("div",{className:"hidden md:flex space-x-8",children:u.map((e,t)=>(0,n.jsx)(s.P.a,{href:e.href,className:"text-white hover:text-blue-400 transition-colors font-medium",onClick:t=>{t.preventDefault(),c(e.href)},initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},whileHover:{scale:1.05},children:e.label},e.href))}),(0,n.jsx)("button",{className:"md:hidden text-white p-2",onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?(0,n.jsx)(o,{size:24}):(0,n.jsx)(l,{size:24})})]}),(0,n.jsx)(s.P.div,{className:`md:hidden ${e?"block":"hidden"}`,initial:{opacity:0,height:0},animate:{opacity:+!!e,height:e?"auto":0},transition:{duration:.3},children:(0,n.jsx)("div",{className:"py-4 space-y-2",children:u.map(e=>(0,n.jsx)("a",{href:e.href,className:"block text-white hover:text-blue-400 transition-colors py-2 px-4 rounded",onClick:t=>{t.preventDefault(),c(e.href)},children:e.label},e.href))})})]})})}},6256:(e,t,i)=>{"use strict";i.d(t,{default:()=>p});var n=i(687),r=i(1121),s=i(7472),a=i(2688);let o=(0,a.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),l=(0,a.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),u=(0,a.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),c=(0,a.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),d=(0,a.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),h=(0,a.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),p=()=>{let[e,t]=(0,s.Wx)({triggerOnce:!0,threshold:.1}),i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,n.jsx)("section",{id:"services-section",className:"py-20 bg-white",ref:e,children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,n.jsx)("h1",{className:"text-6xl font-bold text-gray-200 mb-2",children:"Services"}),(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-800 -mt-8 relative z-10",children:"Services"}),(0,n.jsx)("p",{className:"text-gray-600 mt-4 text-lg max-w-2xl mx-auto",children:"Far far away, behind the word mountains, far from the countries Vokalia and Consonantia"})]}),(0,n.jsx)(r.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",animate:t?"visible":"hidden",children:[{icon:o,title:"Web Design",description:"Creating beautiful and functional web designs that engage users and drive results."},{icon:l,title:"Photography",description:"Professional photography services for events, portraits, and commercial projects."},{icon:u,title:"Web Developer",description:"Full-stack web development using modern technologies and best practices."},{icon:c,title:"App Developing",description:"Mobile and web application development with focus on user experience."},{icon:d,title:"Branding",description:"Complete branding solutions including logo design and brand identity."},{icon:h,title:"Product Strategy",description:"Strategic planning and consultation for digital product development."}].map(e=>{let t=e.icon;return(0,n.jsx)(r.P.div,{className:"group",variants:i,children:(0,n.jsxs)(r.P.div,{className:"bg-gray-50 hover:bg-blue-50 p-8 rounded-lg text-center transition-all duration-300 cursor-pointer h-full",whileHover:{y:-10,boxShadow:"0 20px 40px rgba(0,0,0,0.1)"},transition:{duration:.3},children:[(0,n.jsx)(r.P.div,{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 group-hover:bg-blue-200 rounded-full mb-6 transition-colors duration-300",whileHover:{scale:1.1,rotate:5},transition:{duration:.3},children:(0,n.jsx)(t,{size:32,className:"text-blue-600 group-hover:text-blue-700 transition-colors duration-300"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4 group-hover:text-blue-700 transition-colors duration-300",children:e.title}),(0,n.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})},e.title)})}),(0,n.jsx)(r.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:1},children:(0,n.jsx)(r.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Get Started"})})]})})}},6341:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return p}});let n=i(9551),r=i(1959),s=i(2437),a=i(4396),o=i(8034),l=i(5526),u=i(2887),c=i(4722),d=i(6143),h=i(7912);function p(e,t,i){let r=(0,n.parse)(e.url,!0);for(let e of(delete r.search,Object.keys(r.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),s=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||s||t.includes(e)||i&&Object.keys(i.groups).includes(e))&&delete r.query[e]}e.url=(0,n.format)(r)}function m(e,t,i){if(!i)return e;for(let n of Object.keys(i.groups)){let r,{optional:s,repeat:a}=i.groups[n],o=`[${a?"...":""}${n}]`;s&&(o=`[${o}]`);let l=t[n];r=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,r)}return e}function f(e,t,i,n){let r={};for(let s of Object.keys(t.groups)){let a=e[s];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let o=i[s],l=t.groups[s].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete e[s]),a&&"string"==typeof a&&t.groups[s].repeat&&(a=a.split("/")),a&&(r[s]=a)}return{params:r,hasValidParams:!0}}function g({page:e,i18n:t,basePath:i,rewrites:n,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let y,v,x;return c&&(y=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),x=(v=(0,o.getRouteMatcher)(y))(e)),{handleRewrites:function(a,o){let h={},p=o.pathname,m=n=>{let u=(0,s.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=u(o.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(a,o.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:o.query});if(s.protocol)return!0;if(Object.assign(h,a,m),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(p=o.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),t){let e=(0,r.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(p===e)return!0;if(c&&v){let e=v(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return h},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:i}=y,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let i=(0,h.normalizeNextQueryParam)(e);i&&(n[i]=t,delete n[e])}let r={};for(let e of Object.keys(i)){let s=i[e];if(!s)continue;let a=t[s],o=n[e];if(!a.optional&&!o)return null;r[a.pos]=o}return r}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>y&&x?f(e,y,x,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,y),interpolateDynamicPath:(e,t)=>m(e,t,y)}}function y(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6364:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});var n=i(687),r=i(1121),s=i(474);let a=()=>{let e=e=>{let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})};return(0,n.jsxs)("section",{id:"home-section",className:"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-black/30"}),(0,n.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 items-center",children:[(0,n.jsxs)(r.P.div,{className:"text-white space-y-6",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,n.jsx)(r.P.span,{className:"text-lg text-blue-400 font-medium",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:"Hello!"}),(0,n.jsxs)(r.P.h1,{className:"text-4xl md:text-6xl font-bold leading-tight",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:["I'm ",(0,n.jsx)("span",{className:"text-blue-400",children:"Rizqi Rahmansyah"})]}),(0,n.jsx)(r.P.h2,{className:"text-2xl md:text-3xl text-gray-300 font-light",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},children:"A Web Developer Enthusiast"}),(0,n.jsxs)(r.P.div,{className:"flex flex-col sm:flex-row gap-4 pt-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.8},children:[(0,n.jsx)(r.P.a,{href:"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help",target:"_blank",rel:"noopener noreferrer",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Hire me"}),(0,n.jsx)(r.P.button,{onClick:()=>e("#about-section"),className:"border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-medium transition-all inline-flex items-center justify-center",whileHover:{scale:1.05},whileTap:{scale:.95},children:"About Me"})]})]}),(0,n.jsx)(r.P.div,{className:"relative",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.3},children:(0,n.jsxs)("div",{className:"relative w-full max-w-md mx-auto",children:[(0,n.jsx)(r.P.div,{className:"relative z-10",whileHover:{scale:1.05},transition:{duration:.3},children:(0,n.jsx)(s.default,{src:"/images/me2.png",alt:"Rizqi Rahmansyah",width:400,height:500,className:"w-full h-auto rounded-lg shadow-2xl",priority:!0})}),(0,n.jsx)(r.P.div,{className:"absolute -top-4 -right-4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl",animate:{scale:[1,1.2,1],opacity:[.3,.6,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut"}}),(0,n.jsx)(r.P.div,{className:"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl",animate:{scale:[1.2,1,1.2],opacity:[.4,.2,.4]},transition:{duration:4,repeat:1/0,ease:"easeInOut"}})]})})]})}),(0,n.jsx)(r.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:1.2},children:(0,n.jsx)(r.P.div,{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,n.jsx)(r.P.div,{className:"w-1 h-3 bg-white rounded-full mt-2",animate:{opacity:[1,0,1]},transition:{duration:2,repeat:1/0}})})})]})}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,i){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var r={},s=t.split(n),a=(i||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==r[c]&&(r[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return r},t.serialize=function(e,t,n){var s=n||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!r.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!r.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!r.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,i=encodeURIComponent,n=/; */,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6533:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let n=i(4985),r=i(740),s=i(687),a=r._(i(3210)),o=n._(i(1215)),l=n._(i(512)),u=i(4953),c=i(2756),d=i(7903);i(148);let h=i(9148),p=n._(i(1933)),m=i(3038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/rizqibennington.github.io/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,i,n,r,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,r=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:n,sizes:r,height:o,width:l,decoding:u,className:c,style:d,fetchPriority:h,placeholder:p,loading:f,unoptimized:v,fill:x,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:P,setShowAltText:j,sizesInput:E,onLoad:S,onError:T,...A}=e,C=(0,a.useCallback)(e=>{e&&(T&&(e.src=e.src),e.complete&&g(e,p,b,w,P,v,E))},[i,p,b,w,P,T,v,E]),R=(0,m.useMergedRef)(t,C);return(0,s.jsx)("img",{...A,...y(h),loading:f,width:l,height:o,decoding:u,"data-nimg":x?"fill":"1",className:c,style:d,sizes:r,srcSet:n,src:i,ref:R,onLoad:e=>{g(e.currentTarget,p,b,w,P,v,E)},onError:e=>{j(!0),"empty"!==p&&P(!0),T&&T(e)}})});function x(e){let{isAppRouter:t,imgAttributes:i}=e,n={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...y(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...n},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(h.RouterContext),n=(0,a.useContext)(d.ImageConfigContext),r=(0,a.useMemo)(()=>{var e;let t=f||n||c.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),r=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:r,qualities:s}},[n]),{onLoad:o,onLoadingComplete:l}=e,m=(0,a.useRef)(o);(0,a.useEffect)(()=>{m.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[y,b]=(0,a.useState)(!1),[w,P]=(0,a.useState)(!1),{props:j,meta:E}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:r,blurComplete:y,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{...j,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:P,sizesInput:e.sizes,ref:t}),E.priority?(0,s.jsx)(x,{isAppRouter:!i,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let n=i(2785),r=i(3736);function s(e){if(e.startsWith("/"))return(0,r.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7472:(e,t,i)=>{"use strict";i.d(t,{Wx:()=>c});var n=i(3210),r=Object.defineProperty,s=(e,t,i)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,a=new Map,o=new WeakMap,l=0,u=void 0;function c({threshold:e,delay:t,trackVisibility:i,rootMargin:r,root:s,triggerOnce:d,skip:h,initialInView:p,fallbackInView:m,onChange:f}={}){var g;let[y,v]=n.useState(null),x=n.useRef(f),[b,w]=n.useState({inView:!!p,entry:void 0});x.current=f,n.useEffect(()=>{let n;if(!h&&y)return n=function(e,t,i={},n=u){if(void 0===window.IntersectionObserver&&void 0!==n){let r=e.getBoundingClientRect();return t(n,{isIntersecting:n,target:e,intersectionRatio:"number"==typeof i.threshold?i.threshold:0,time:0,boundingClientRect:r,intersectionRect:r,rootBounds:r}),()=>{}}let{id:r,observer:s,elements:c}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var i;return`${t}_${"root"===t?!(i=e.root)?"0":(o.has(i)||(l+=1,o.set(i,l.toString())),o.get(i)):e[t]}`}).toString(),i=a.get(t);if(!i){let n,r=new Map,s=new IntersectionObserver(t=>{t.forEach(t=>{var i;let s=t.isIntersecting&&n.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=s),null==(i=r.get(t.target))||i.forEach(e=>{e(s,t)})})},e);n=s.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),i={id:t,observer:s,elements:r},a.set(t,i)}return i}(i),d=c.get(e)||[];return c.has(e)||c.set(e,d),d.push(t),s.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(c.delete(e),s.unobserve(e)),0===c.size&&(s.disconnect(),a.delete(r))}}(y,(e,t)=>{w({inView:e,entry:t}),x.current&&x.current(e,t),t.isIntersecting&&d&&n&&(n(),n=void 0)},{root:s,rootMargin:r,threshold:e,trackVisibility:i,delay:t},m),()=>{n&&n()}},[Array.isArray(e)?e.toString():e,y,s,r,d,h,i,m,t]);let P=null==(g=b.entry)?void 0:g.target,j=n.useRef(void 0);y||!P||d||h||j.current===P||(j.current=P,w({inView:!!p,entry:void 0}));let E=[v,b.inView,b.entry];return E.ref=E[0],E.inView=E[1],E.entry=E[2],E}n.Component},7576:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\rizqibennington.github.io\\\\portfolio-nextjs\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ProjectsSection.tsx","default")},7752:(e,t,i)=>{Promise.resolve().then(i.bind(i,4712)),Promise.resolve().then(i.bind(i,1195)),Promise.resolve().then(i.bind(i,2119)),Promise.resolve().then(i.bind(i,2246)),Promise.resolve().then(i.bind(i,4230)),Promise.resolve().then(i.bind(i,2234)),Promise.resolve().then(i.bind(i,7576)),Promise.resolve().then(i.bind(i,610)),Promise.resolve().then(i.bind(i,200)),Promise.resolve().then(i.bind(i,3041))},7755:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=i(3210),r=()=>{},s=()=>{};function a(e){var t;let{headManager:i,reduceComponentsToState:a}=e;function o(){if(i&&i.mountedInstances){let t=n.Children.toArray(Array.from(i.mountedInstances).filter(Boolean));i.updateHead(a(t,e))}}return null==i||null==(t=i.mountedInstances)||t.add(e.children),o(),r(()=>{var t;return null==i||null==(t=i.mountedInstances)||t.add(e.children),()=>{var t;null==i||null==(t=i.mountedInstances)||t.delete(e.children)}}),r(()=>(i&&(i._pendingUpdate=o),()=>{i&&(i._pendingUpdate=o)})),s(()=>(i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null),()=>{i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null)})),null}},7903:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.ImageConfigContext},7941:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var n=i(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)("/rizqibennington.github.io",await e.params,"favicon.ico")+""}]},8034:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return r}});let n=i(4827);function r(e){let{re:t,groups:i}=e;return e=>{let r=t.exec(e);if(!r)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(i)){let i=r[t.pos];void 0!==i&&(t.repeat?a[e]=i.split("/").map(e=>s(e)):a[e]=s(i))}return a}}},8212:(e,t,i)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=i(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let n=i(2958),r=i(4722),s=i(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,i){let r=(i?"":"?")+"$",s=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${r}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${r}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${r}`),RegExp(`[\\\\/]${a.icon.filename}${s}${l(a.icon.extensions,t)}${r}`),RegExp(`[\\\\/]${a.apple.filename}${s}${l(a.apple.extensions,t)}${r}`),RegExp(`[\\\\/]${a.openGraph.filename}${s}${l(a.openGraph.extensions,t)}${r}`),RegExp(`[\\\\/]${a.twitter.filename}${s}${l(a.twitter.extensions,t)}${r}`)],u=(0,n.normalizePathSep)(e);return o.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,s.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,r.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,s.isAppRouteRoute)(e)&&u(t,[],!1)}},8481:()=>{},8753:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[447,679],()=>i(5562));module.exports=n})();